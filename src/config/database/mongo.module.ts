import { Logger, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

const logger = new Logger('Database Module');

@Module({
  imports: [
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => {
        return {
          connectionFactory: (connection) => {
            if (connection.readyState === 1) {
              logger.log('Database Connected successfully');
            }
            connection.on('disconnected', () => {
              logger.error('Database disconnected');
            });
            connection.on('error', (error) => {
              logger.error('Database disconnected', error);
            });

            return connection;
          },
          uri: config.get<string>('DB_URL'),
          user: config.get<string>('DB_USER'),
          pass: config.get<string>('DB_PASS'),
        };
      },
    }),
  ],
})
export class MongoModule {}
