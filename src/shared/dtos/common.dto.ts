import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { description } from '../../utils/descriptions';

export class LarkBaseConfigCommonQueryDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.larkBaseSetting.validator.appId,
  })
  @IsNotEmpty()
  @IsString()
  appId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.larkBaseSetting.validator.tableId,
  })
  @IsNotEmpty()
  @IsString()
  tableId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.larkBaseSetting.validator.tableNameType,
  })
  @IsNotEmpty()
  @IsString()
  tableNameType;
}
