import { Injectable } from '@nestjs/common';

@Injectable()
export class StateStoreService {
  private stateStore = new Map<string, any>();

  saveStateStore(state: string, data: any): void {
    this.stateStore.set(state, data);
  }

  verifyStateStore(state: string): boolean {
    return this.stateStore.has(state);
  }

  getStateStore(state: string): any | null {
    return this.stateStore.get(state) ?? null;
  }

  clearStateStore(state: string): boolean {
    return this.stateStore.delete(state);
  }
}
