import { Prop } from '@nestjs/mongoose';
import * as moment from 'moment';

export class BaseSchema<T> {
  toObject(): Partial<T> {
    throw new Error('Method not implemented.');
  }
  toJSON(): Partial<T> {
    throw new Error('Method not implemented.');
  }

  _id: string;

  @Prop({
    default: null,
    get: (data: Date) => {
      return data && moment(data).format('DD-MM-YYYY HH:mm:ss');
    },
  })
  deletedAt: Date;

  @Prop({
    type: Date,
    required: true,
    default: new Date(),
    get: (data: Date) => {
      return data && moment(data).format('DD-MM-YYYY HH:mm:ss');
    },
  })
  createdAt: Date;

  @Prop({
    type: Date,
    required: true,
    default: new Date(),
    get: (data: Date) => {
      return data && moment(data).format('DD-MM-YYYY HH:mm:ss');
    },
  })
  updatedAt: Date;
}
