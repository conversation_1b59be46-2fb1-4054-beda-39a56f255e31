import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../modules/app.module';
import { cronJobFeedbackShopeeToLarkBase } from '../lark-base-job';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    console.log('Starting Cron job');

    await cronJobFeedbackShopeeToLarkBase(app);
    console.log('Finished Cron job');
  } catch (error) {
    console.error('Error executing scheduled task:', error);
  } finally {
    await app.close(); // Close the application context after execution
  }
}

bootstrap();
