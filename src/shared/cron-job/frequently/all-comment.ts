import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../modules/app.module';
import { cronJobUpdateAllItemComment } from '../item-comment-job';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    console.log('Starting Cron job');
    const partnerIds = process.env.SHOPEE_SHOP.split(',');
    for (const partnerId of partnerIds) {
      await cronJobUpdateAllItemComment(app, Number(partnerId));
    }
    console.log('Finished Cron job');
  } catch (error) {
    console.error('Error executing scheduled task:', error);
  } finally {
    await app.close(); // Close the application context after execution
  }
}

bootstrap();
