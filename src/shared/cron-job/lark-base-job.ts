import { INestApplicationContext } from '@nestjs/common';
import { LarkBaseService } from '../../modules/lark/base/lark-base/lark-base.service';
import { AppType, LarkBaseTableName } from '../../utils/enums';

export const cronJobFeedbackShopeeToLarkBase = async (
  app: INestApplicationContext,
) => {
  const larkBaseService = app.get(LarkBaseService);

  await larkBaseService.sendFeedBackShopeeToLarkBase({
    appType: AppType.notifyFeedBackUser,
    env: process.env.LARK_ENV,
    tableNameType: LarkBaseTableName.shopeeItemComment,
  });
};
