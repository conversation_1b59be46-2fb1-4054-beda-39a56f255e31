import { ShopeeItemCommentService } from '../../modules/shopee/comment-list/shopee-comment-list/shopee-comment.service';
import { INestApplicationContext } from '@nestjs/common';

export const cronJobUpdateAllItemComment = async (
  app: INestApplicationContext,
  partnerId: number,
) => {
  const shopeeItemCommentService = app.get(ShopeeItemCommentService);
  await shopeeItemCommentService.getItemCommentsAndUpsertFromApi(partnerId);
};

export const cronJobUpdateLastItemComment = async (
  app: INestApplicationContext,
  partnerId: number,
) => {
  const shopeeItemCommentService = app.get(ShopeeItemCommentService);
  await shopeeItemCommentService.getLastItemCommentsAndUpsertFromApi(partnerId);
};
