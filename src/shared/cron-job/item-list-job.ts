import { ShopeeItemService } from '../../modules/shopee/item/shopee-item/shopee-item.service';
import { INestApplicationContext } from '@nestjs/common';

export const cronJobUpdateItems = async (
  app: INestApplicationContext,
  partnerId: number,
) => {
  const shopeeItemService = app.get(ShopeeItemService);

  await shopeeItemService.getItemsAndUpsertFromApi(partnerId);
  await shopeeItemService.getDetailItemsAndUpsertFromApi(partnerId);
};
