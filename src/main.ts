import { NestFactory } from '@nestjs/core';
import { json, urlencoded } from 'express';
import { AppModule } from './modules/app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

const PORT = process.env.PORT || 6000;

async function bootstrap() {
  const logger = new Logger('Main');
  const app = await NestFactory.create(AppModule);
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));

  // Set global prefix: medicar-be/v1
  if (process.env.NODE_ENV === 'prod') {
    app.setGlobalPrefix('medicar-be/v1');
  }

  app.useGlobalPipes(new ValidationPipe());

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Medicar API') // Set the title of the API
    .setDescription('The API Medicar description') // Set the description
    .setVersion('1.0') // Set the version of the API
    // .addBearerAuth() // If you're using Bearer JWT authentication
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document); // Swagger UI will be available at /api

  await app.listen(PORT);
  logger.log(`Application running on http://localhost:${PORT}`);
}
bootstrap();
