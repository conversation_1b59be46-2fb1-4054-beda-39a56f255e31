export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

export function toSnakeCase(str: string): string {
  return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}

export function convertToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(convertToCamelCase);
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      if (key === '_id') return acc;
      acc[toCamelCase(key)] = convertToCamelCase(obj[key]);
      return acc;
    }, {} as any);
  }
  return obj;
}

export function convertToSnakeCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(convertToSnakeCase);
  } else if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((acc, key) => {
      if (key === 'id') {
        acc['_id'] = obj[key];
        return acc;
      }
      acc[toSnakeCase(key)] = convertToSnakeCase(obj[key]);
      return acc;
    }, {} as any);
  }
  return obj;
}
