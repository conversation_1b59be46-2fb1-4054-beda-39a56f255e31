import { RouteName } from './enums';

export const description = {
  larkBaseSetting: {
    name: 'Cấu hình Lark Base',
    validator: {
      appId: 'LarkBase Setting appId',
      appToken: 'LarkBase Setting appToken',
      tableId: 'LarkBase Setting tableId',
      tableNameType: 'LarkBase Setting tableNameType',
    },
  },
  auths: {
    name: 'OAuth',
    validator: {
      callBack: 'Call back application',
      code: 'code',
      shopeeShopId: 'shopId của shopee',
    },
  },
  healthz: {
    name: 'heart Beat',
  },
  common: {
    status: 'Trạng thái',
    images: 'Hình ảnh',
  },
  httpStatus: {
    ok: 'Successfully',
    forbidden: 'Forbidden.',
    unAuthorized: 'Unauthorized.',
    badRequest: 'Unauthorized.',
    interalServer: 'Internal Server Exception.',
    notFound: 'Not Found.',
  },
  controller: {
    gets: 'L<PERSON>y danh sách',
    getOne: '<PERSON><PERSON><PERSON> chi tiết',
    callBack: '<PERSON><PERSON><PERSON> gọi lại',
    webhook: 'WebHook',
    post: 'Tạo mới',
    put: 'Cập nhật',
    delete: 'Xoá',
    clean: 'Quét dọn',
    getsDescription: 'Tính năng này được sử dụng để lấy toàn bộ',
    getOneDescription: 'Tính năng này được sử dụng để lấy chi tiết',
    postDescription: 'Tính năng này được sử dụng để tạo',
    putDescription: 'Tính năng này được sử dụng để cập nhật',
    deleteDescription: 'Tính năng này được sử dụng để xoá',
    login: 'Tính năng đăng nhập',
  },
};

export const getDescription = (RouteName: RouteName, method: string) => {
  const masterData = description[RouteName] || {
    name: 'chưa xác định',
  };
  return `${method} ${masterData.name}`;
};
