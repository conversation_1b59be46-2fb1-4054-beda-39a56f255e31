// String enum
export enum RouteName {
  healthz = 'healthz',
  auths = 'auths',
  shopeeItem = 'shopee-items',
  shopeeItemComment = 'shopee-item-comments',
  shopeeOrder = 'shopee-orders',
  larks = 'larks',
  larkBases = 'lark-bases',
}

export enum ApplicationName {
  shopee = 'shopee',
}

export enum Environment {
  sandBox = 'sandbox',
  live = 'live',
}

export enum AppType {
  notifyFeedBackUser = 'notifyFeedBackUser',
}

export enum BotTemplate {
  welcomeGroup = 'welcomeGroup',
  sendFeedback = 'sendFeedback',
}

export enum NotificationStatus {
  success = 'success',
  fail = 'fail',
}

export enum NotificationType {
  feedBack = 'feedBack',
  base = 'base',
}

export enum RedisKey {
  shopee = 'SHOPEE',
  lark = 'LARK',
}

export enum LarkBaseTableName {
  shopeeItemComment = 'shopee-item-comment',
  shopeeAds = 'shopee-ads',
}
