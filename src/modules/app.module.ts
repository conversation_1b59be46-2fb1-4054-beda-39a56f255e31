import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongoModule } from '../config/database/mongo.module';
import { AuthModule } from './auth/auth/auth.module';
import { ShopeeConfigurationModule } from './shopee/configuration/shopee-configuration/shopee-configuration.module';
import { ShopeeItemModule } from './shopee/item/shopee-item/shopee-item.module';
import { ShopeeItemCommentModule } from './shopee/comment-list/shopee-comment-list/shopee-comment.module';
import { ShopeeAdsModule } from './shopee/ads/shopee-ads/shopee-ads.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { LarkAppModule } from './lark/app/lark-app/lark-app.module';
import { LarkModule } from './lark/lark.module';
import { LarkChatSettingModule } from './lark/chat-setting/lark-chat-setting/lark-chat-setting.module';
import { LarkNotificationModule } from './lark/notification/lark-notification/lark-notification.module';
import { LarkBaseSettingModule } from './lark/base-setting/lark-base-setting/lark-base-setting.module';
import { LarkBaseTableModule } from './lark/base-table/lark-base-table/lark-base-table.module';
import { RedisModule } from './platforms/storages/storages/redis/redis.module';
import { ShopeeOrderModule } from './shopee/order/shopee-order/shopee-order.module';
import { UserModule } from './auth/auth/users/user.module';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './auth/auth/auth.guard';
import { LarkBaseModule } from './lark/base/lark-base/lark-base.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    RedisModule,
    EventEmitterModule.forRoot(),
    MongoModule,
    AuthModule,
    ShopeeItemModule,
    ShopeeItemCommentModule,
    ShopeeConfigurationModule,
    ShopeeOrderModule,
    ShopeeAdsModule,
    LarkAppModule,
    LarkModule,
    LarkChatSettingModule,
    LarkNotificationModule,
    LarkBaseSettingModule,
    LarkBaseTableModule,
    UserModule,
    LarkBaseModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class AppModule {}
