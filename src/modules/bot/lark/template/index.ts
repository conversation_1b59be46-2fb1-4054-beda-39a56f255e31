const welcomeGroup = ({ chatId }: { chatId: string }) => {
  return {
    config: {
      wide_screen_mode: true,
      enable_forward: true,
    },
    header: {
      template: 'blue',
      title: {
        tag: 'plain_text',
        content: 'Thông báo từ Bot',
      },
    },
    elements: [
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content:
            '🤖 Xin chào, tôi là **Medicar Bot**.\nTôi sẽ hỗ trợ thông báo khi phát Feedback từ khách hàng trên Shopee.',
        },
      },
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content: `📌 **Chat ID** : **${chatId}**`,
        },
      },
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content: '➡️ Hãy thêm ID trên vào phần cấu hình Thông báo.',
        },
      },
    ],
  };
};

const sendFeedback = ({
  itemName,
  ratingStar,
  ratingStarIcon,
  buyerUsername,
  comment,
  orderSn,
  createTime,
}: {
  itemName: string;
  ratingStar: number;
  ratingStarIcon: string;
  buyerUsername: string;
  comment: string;
  orderSn: string;
  createTime: string;
}) => {
  return {
    config: {
      wide_screen_mode: true,
      enable_forward: true,
    },
    header: {
      template: ratingStar < 5 ? 'red' : 'green',
      title: {
        tag: 'plain_text',
        content: `[${orderSn}] - Vừa đánh giá ${ratingStar} sao - Ngày ${createTime}`,
      },
    },
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'div',
                text: {
                  content: `**🛒 Mã đơn:**\n${orderSn}`,
                  tag: 'lark_md',
                },
              },
            ],
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'div',
                text: {
                  content: `**🕐 Ngày gửi đánh giá:**\n${createTime}`,
                  tag: 'lark_md',
                },
              },
            ],
          },
        ],
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'div',
                text: {
                  content: `**👤 Khách hàng:**\n${buyerUsername}`,
                  tag: 'lark_md',
                },
              },
            ],
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'div',
                text: {
                  content: `**📝 Đánh giá:**\n${ratingStarIcon}`,
                  tag: 'lark_md',
                },
              },
            ],
          },
        ],
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'div',
                text: {
                  content: `**📦 Sản phẩm:**\n${itemName}`,
                  tag: 'lark_md',
                },
              },
            ],
          },
        ],
      },
      {
        tag: 'hr',
      },
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content: `**Nội dung**: ${comment}`,
        },
      },
      {
        tag: 'hr',
      },
      {
        tag: 'action',
        actions: [
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: 'Xem chi tiết',
            },
            type: 'primary',
            url: 'https://banhang.shopee.vn/portal/settings/shop/rating?pageNumber=1&fromPageNumber=1&cursor=0&pageSize=20&replied=TO_REPLY&ratingStar=2&ratingStar=1&ratingStar=3',
          },
        ],
      },
    ],
  };
};

export default {
  welcomeGroup,
  sendFeedback,
};
