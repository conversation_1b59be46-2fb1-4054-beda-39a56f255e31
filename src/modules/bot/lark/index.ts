import { RedisService } from './../../storages/redis/redis.service';
import axios from 'axios';
import { LarkApiUrl } from './config';
import { convertToSnakeCase } from '../../../utils/case-converter';
import { HttpStatus, Injectable } from '@nestjs/common';
import { BotTemplate, RedisKey } from '../../../utils/enums';
import messageTemplate from './template';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class BotLark {
  constructor(
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  private getCommonHeader(config) {
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${config.tenantAccessToken}`,
    };
  }

  private async makeRequest(
    method,
    config,
    { url, headers = undefined, params = undefined, data = undefined },
    retry = 0,
  ) {
    try {
      const response = await axios({
        url: `${config.baseUrl}${url}`,
        method,
        headers: {
          ...this.getCommonHeader(config),
          ...headers,
        },
        params:
          params &&
          convertToSnakeCase({
            ...params,
          }),
        data: data && convertToSnakeCase({ ...data }),
      });
      return response.data;
    } catch (error) {
      switch (error.status) {
        case HttpStatus.BAD_REQUEST: {
          if (retry === 3) {
            console.log('Max retry api 3 times');
            break;
          }
          if (
            [
              'Invalid access token for authorization. Please make a request with token attached.',
            ].includes(error.response.data?.msg)
          ) {
            const result = await this.getAccessToken(config);
            if (result['tenant_access_token']) {
              const newConfig = {
                ...config,
                tenantAccessToken: result['tenant_access_token'],
                expire: result['expire'],
              };
              this.redisService.setValue(
                `${RedisKey.lark}_${newConfig.appId}`,
                newConfig,
              );

              this.eventEmitter.emit('larkApp.update', newConfig);

              return this.makeRequest(
                method,
                newConfig,
                {
                  url,
                  headers,
                  params: params,
                  data,
                },
                retry++,
              );
            }
          }
        }
      }
      console.log('error', error);
      throw error;
    }
  }

  public async getAccessToken(config) {
    const url = config.baseUrl + LarkApiUrl.token;
    const response = await axios({
      url,
      method: 'post',
      data: convertToSnakeCase({
        appId: config.appId,
        appSecret: config.appSecret,
      }),
    });
    return response.data;
  }

  public async sendMessage(
    {
      chatId,
      text,
      msgType = 'text',
      content = {},
      card = {},
    }: {
      chatId: string;
      text?: string;
      msgType?: string;
      content?: Object;
      card?: Object;
    },
    appId: string,
  ) {
    const config = await this.redisService.getValue(
      `${RedisKey.lark}_${appId}`,
    );
    return this.makeRequest('post', config, {
      url: LarkApiUrl.sendMessage,
      data: convertToSnakeCase({
        chatId,
        msgType: msgType,
        content: content && Object.keys(content).length ? content : { text },
        card: card && Object.keys(card).length ? card : undefined,
      }),
    });
  }

  public async sendMessageToExternalGroup({
    chatId,
    baseUrl,
    text,
    msgType = 'text',
    content = {},
    card = {},
  }: {
    chatId: string;
    baseUrl: string;
    text?: string;
    msgType?: string;
    content?: Object;
    card?: Object;
  }) {
    const response = await axios({
      url: `${baseUrl}${LarkApiUrl.sendMessageExternalGroup.replace(
        '{chatId}',
        chatId,
      )}`,
      method: 'post',
      data: convertToSnakeCase({
        msgType: msgType,
        content: content && Object.keys(content).length ? content : { text },
        card: card && Object.keys(card).length ? card : undefined,
      }),
    });
    return response.data;
  }

  public getTemplate(template: BotTemplate, params: any) {
    return messageTemplate[template](params);
  }
}
