import { Module } from '@nestjs/common';
import { ShopeeApiService } from './shopee-api-service';
import { ShopeeConfigurationModule } from '../shopee-configuration/shopee-configuration.module';
import { ShopeeApi } from '../ecommerces/shopee';
import { RedisModule } from '../storages/redis/redis.module';

@Module({
  imports: [ShopeeConfigurationModule, RedisModule],
  providers: [ShopeeApiService, ShopeeApi],
  exports: [ShopeeApiService],
})
export class ShopeeApiModule {}
