import { Injectable } from '@nestjs/common';
import { ShopeeApi } from '../../../ecommerces/shopee';
import {
  ShopeeApiConfigType,
  ShopeeApiShopProfile,
  ShopeeGetCampainInfo,
  ShopeeGetCampainList,
} from '../../../ecommerces/shopee/types';

@Injectable()
export class ShopeeApiService {
  constructor(private readonly shopeeApi: ShopeeApi) {}

  async getAccessToken(code: string, config: ShopeeApiConfigType) {
    return this.shopeeApi.getAcessToken(code, config);
  }

  async getListItem(offset = 0, partnerId: number) {
    return this.shopeeApi.getItemList(
      {
        offset,
      },
      partnerId,
    );
  }

  async getItem(itemId: string, partnerId: number) {
    return this.shopeeApi.getItemDetail(itemId, partnerId);
  }

  async getItemComment(
    itemId: number,
    commentId: number,
    pageSize: number,
    page: number,
    partnerId: number,
  ) {
    return this.shopeeApi.getItemComment(
      {
        itemId,
        commentId,
        page,
        pageSize,
      },
      partnerId,
    );
  }

  async getShopProfile(params: ShopeeApiShopProfile) {
    return this.shopeeApi.getShopProfile(params);
  }

  async getProductLevelCampaignIdList(params: ShopeeGetCampainList) {
    return this.shopeeApi.getProductLevelCampaignIdList(params);
  }

  async getProductLevelCampaignSettingInfo(params: ShopeeGetCampainInfo) {
    return this.shopeeApi.getProductLevelCampaignSettingInfo(params);
  }
}
