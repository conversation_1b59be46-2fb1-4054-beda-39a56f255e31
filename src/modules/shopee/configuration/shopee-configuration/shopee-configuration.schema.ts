import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../../../shared/base/base.schema';
import { convertToCamelCase } from '../../../../utils/case-converter';
import { Environment } from '../../../../utils/enums';

export type ShopeeConfigurationDocument = HydratedDocument<ShopeeConfiguration>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (_, ret) => {
      return ret;
    },
  },
})
export class ShopeeConfiguration extends BaseSchema<ShopeeConfiguration> {
  _id: any; // Thêm _id để TypeScript nhận diện
  @Prop({
    type: String,
    required: true,
  })
  baseUrl: string;

  @Prop({
    type: Number,
    required: true,
  })
  partnerId: number;

  @Prop({
    type: String,
    required: true,
  })
  partnerKey: string;

  @Prop({
    type: Number,
    required: false,
  })
  shopId: number;

  @Prop({
    type: String,
    required: false,
  })
  shopName: string;

  @Prop({
    type: Number,
    required: false,
  })
  mainAccountId: number;

  @Prop({
    type: String,
    required: false,
  })
  refreshToken: string;

  @Prop({
    type: String,
    required: false,
  })
  accessToken: string;

  @Prop({
    type: Number,
    required: false,
  })
  expireIn: number;

  @Prop({
    type: String,
    enum: Object.values(Environment),
    required: true,
  })
  env: string;
}

export const ShopeeConfigurationSchema =
  SchemaFactory.createForClass(ShopeeConfiguration);
