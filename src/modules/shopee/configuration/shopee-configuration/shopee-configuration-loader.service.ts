import { RedisService } from '../../../platforms/storages/storages/redis/redis.service';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { ShopeeConfigurationService } from './shopee-configuration.service';
import { Environment, RedisKey } from '../../../../utils/enums';
import { showShopeeConfigurationByTable } from '../../../../utils/logs';

@Injectable()
export class ShopeeConfigurationLoaderService implements OnModuleInit {
  constructor(
    private readonly shopeeConfigurationService: ShopeeConfigurationService,
    private readonly redisService: RedisService,
  ) {}

  async loadConfiguration() {
    const shopeeEnv = process.env.SHOPEE_ENV;
    const configurations = await this.shopeeConfigurationService.find({
      deletedAt: null,
      env: shopeeEnv,
    });

    console.log(`Set Shopee Configuration from Redis: ${shopeeEnv}`);
    for (const config of configurations) {
      if (config.accessToken) {
        this.redisService.setValue(
          `${RedisKey.shopee}_${config.partnerId}`,
          config,
        );
      }
      showShopeeConfigurationByTable(config);
    }
  }

  async onModuleInit() {
    const liveConfig = await this.shopeeConfigurationService.findOne({
      deletedAt: null,
      env: Environment.live,
    });

    if (!liveConfig) {
      this.shopeeConfigurationService.create({
        baseUrl: 'https://partner.shopeemobile.com',
        partnerId: 2010727,
        partnerKey:
          '7858665878587079674a4b4d41596f61786b6a516f476461495669676f48514c',
        env: Environment.live,
        shopId: 818614417,
      });
    }

    const sandBoxConfig = await this.shopeeConfigurationService.findOne({
      deletedAt: null,
      env: Environment.sandBox,
    });
    if (!sandBoxConfig) {
      this.shopeeConfigurationService.create({
        baseUrl: 'https://partner.test-stable.shopeemobile.com',
        partnerId: 1271219,
        partnerKey:
          '794e6c4f79775274764764426c434f424a436a6b4c5462716676464c734e747a',
        env: Environment.sandBox,
        shopId: null,
      });
    }
    this.loadConfiguration();
  }
}
