import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  ShopeeConfiguration,
  ShopeeConfigurationSchema,
} from './shopee-configuration.schema';
import { ShopeeConfigurationLoaderService } from './shopee-configuration-loader.service';
import { ShopeeConfigurationService } from './shopee-configuration.service';
import { RedisModule } from '../../../platforms/storages/storages/redis/redis.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ShopeeConfiguration.name,
        schema: ShopeeConfigurationSchema,
        collection: 'shopee-configurations',
      },
    ]),
    RedisModule,
  ],
  controllers: [],
  providers: [ShopeeConfigurationLoaderService, ShopeeConfigurationService],
  exports: [MongooseModule, ShopeeConfigurationService],
})
export class ShopeeConfigurationModule {}
