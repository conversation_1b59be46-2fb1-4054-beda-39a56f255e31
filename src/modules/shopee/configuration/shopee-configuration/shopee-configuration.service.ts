import { FilterQuery, Model, QueryOptions, Types } from 'mongoose';
import { Injectable, NotFoundException, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ShopeeConfiguration } from './shopee-configuration.schema';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class ShopeeConfigurationService {
  constructor(
    @InjectModel(ShopeeConfiguration.name)
    private shopeeConfiguration: Model<ShopeeConfiguration>,
  ) {}

  @OnEvent('shoppeeConfiguration.update')
  async handleShopeeConfigurationUpdate(payload) {
    const config = await this.findExistCurrentEnv();
    await this.update(config._id, {
      accessToken: payload.accessToken,
      refreshToken: payload.refreshToken,
      expireIn: payload.expireIn,
    });
  }

  async findExistCurrentEnv(): Promise<ShopeeConfiguration> {
    const shopeeEnv = process.env.SHOPEE_ENV;
    const query = await this.shopeeConfiguration.findOne({
      env: shopeeEnv,
      deleteAt: null,
    });
    if (!query) {
      throw new NotFoundException(`Not found config enviroment ${shopeeEnv}`);
    }
    return query;
  }

  find(
    filter?: FilterQuery<ShopeeConfiguration>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<ShopeeConfiguration[]> {
    return this.shopeeConfiguration.find(filter, projection, options);
  }

  findOne(
    filter?: FilterQuery<ShopeeConfiguration>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<ShopeeConfiguration> {
    return this.shopeeConfiguration.findOne(filter, projection, options);
  }

  async create(data): Promise<ShopeeConfiguration> {
    return this.shopeeConfiguration.create(data);
  }

  async update(id: string, data): Promise<any> {
    return this.shopeeConfiguration.updateOne(
      {
        _id: new Types.ObjectId(id),
      },
      {
        $set: data,
      },
    );
  }
}
