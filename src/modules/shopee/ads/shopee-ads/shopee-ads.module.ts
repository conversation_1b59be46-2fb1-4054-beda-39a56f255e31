import { Module } from '@nestjs/common';
import { ShopeeApiModule } from '../shopee-api/shopee-api.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ShopeeAdsController } from './shopee-ads.controller';
import { ShopeeAdsService } from './shopee-ads.service';
import {
  ShopeeCampaign,
  ShopeeCampaignSchema,
} from './schema/shopee-campaign.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ShopeeCampaign.name,
        schema: ShopeeCampaignSchema,
        collection: 'shopee-campaigns',
      },
    ]),
    ShopeeApiModule,
  ],
  controllers: [ShopeeAdsController],
  providers: [ShopeeAdsService],
  exports: [ShopeeAdsService],
})
export class ShopeeAdsModule {}
