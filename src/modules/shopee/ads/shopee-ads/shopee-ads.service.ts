import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LarkService } from '../../../lark/lark.service';
import { ShopeeApiService } from '../../api/shopee-api/shopee-api-service';
import { ShopeeCampaign } from './schema/shopee-campaign.schema';

@Injectable()
export class ShopeeAdsService {
  private readonly logger = new Logger(ShopeeAdsService.name);

  constructor(
    @InjectModel(ShopeeCampaign.name)
    private readonly shopeeCampaignModel: Model<ShopeeCampaign>,
    private readonly shopeeApiService: ShopeeApiService,
  ) {}

  async getCampaignsAndUpsert(partnerId: number) {
    let offset = 0;
    const pageSize = 10;

    try {
      while (true) {
        const result =
          await this.shopeeApiService.getProductLevelCampaignIdList({
            partnerId,
            offset,
            pageSize,
            adType: ['all', ''],
          });

        const campaignList = result.response.campaign_list || [];
        if (campaignList.length) {
          const campaignConverted = campaignList.map((campaign) => ({
            adType: campaign['ad_type'],
            campaignId: campaign['campaign_id'],
            partnerId,
          }));

          await this.bulkUpsert(campaignConverted);
        }

        if (!result.response['has_next_page']) {
          break;
        }
        offset += pageSize;
      }

      return {
        success: true,
        message: 'Ads list fetched and updated successfully',
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch ads list: ${error.message}`,
        error.stack,
      );
      return { success: false, message: error.message };
    }
  }

  async getCampaignDetails(partnerId: number) {
    let page = 1;
    while (true) {
      const limit = 100;
      const skip = (page - 1) * limit;

      const campaigns = await this.shopeeCampaignModel.find(
        {
          partnerId,
          deletedAt: null,
        },
        {
          _id: 1,
          campaignId: 1,
        },
        { limit: limit, skip: skip },
      );

      if (!campaigns.length) return;

      const result =
        await this.shopeeApiService.getProductLevelCampaignSettingInfo({
          infoTypeList: ['1', '2', '3', '4'].join(','),
          campaignIdList: campaigns
            .map((campaign) => campaign.campaignId)
            .join(','),
          partnerId,
        });
      const campaignList = result.response.campaign_list || [];

      if (campaignList.length) {
        const campaignConverted = campaignList.map((campaign) => ({
          partnerId,
          campaignId: campaign['campaign_id'],
          commonInfo: campaign['common_info']
            ? {
                adType: campaign['common_info']['ad_type'],
                adName: campaign['common_info']['ad_name'],
                campaignStatus: campaign['common_info']['campaign_status'],
                biddingMethod: campaign['common_info']['bidding_method'],
                campaignPlacement:
                  campaign['common_info']['campaign_placement'],
                campaignBudget: campaign['common_info']['campaign_budget'],
                campaignDuration: campaign['common_info']['campaign_duration']
                  ? {
                      startTime:
                        campaign['common_info']['campaign_duration'][
                          'start_time'
                        ],
                      endTime:
                        campaign['common_info']['campaign_duration'][
                          'end_time'
                        ],
                    }
                  : {},
                itemIdList: campaign['common_info']['item_id_list'],
              }
            : {},
          manualBiddingInfo: campaign['manual_bidding_info']
            ? {
                enhancedCpc: campaign['manual_bidding_info']['enhanced_cpc'],
                selectedKeywords:
                  campaign['manual_bidding_info']['selected_keywords'],
                discoveryAdsLocations:
                  campaign['manual_bidding_info']['discovery_ads_locations'],
              }
            : {},
          autoBiddingInfo: campaign['auto_bidding_info']
            ? {
                roasTarget: campaign['auto_bidding_info']['roas_target'],
              }
            : {},
          autoProductAdsInfo: campaign['auto_product_ads_info']
            ? {
                productName: campaign['auto_product_ads_info']['product_name'],
                status: campaign['auto_product_ads_info']['status'],
                itemId: campaign['auto_product_ads_info']['item_id'],
              }
            : {},
          metadata: campaign,
        }));
        await this.bulkUpsert(campaignConverted);
      }

      page++;
    }
  }

  async bulkUpsert(campaigns: Partial<ShopeeCampaign>[]) {
    const bulkOps = campaigns.map((campaign) => ({
      updateOne: {
        filter: {
          campaignId: Number(campaign.campaignId),
          partnerId: Number(campaign.partnerId),
        },
        update: {
          $set: campaign,
        },
        upsert: true,
      },
    }));

    return this.shopeeCampaignModel.bulkWrite(bulkOps);
  }

  async find(filter = {}, projection = {}, options = {}) {
    return this.shopeeCampaignModel.find(filter, projection, options).exec();
  }

  async findOne(filter = {}) {
    return this.shopeeCampaignModel.findOne(filter).exec();
  }
}
