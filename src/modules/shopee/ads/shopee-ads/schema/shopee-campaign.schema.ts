import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from '../../../shared/base/base.schema';

class ShopeeCampainAutoBiddingInfo {
  @Prop({
    type: Number,
    required: true,
  })
  roasTarget: number;
}

class ShopeeCampainAutoProductAdsInfo {
  @Prop({
    type: String,
    required: true,
  })
  productName: string;

  @Prop({
    type: String,
    required: true,
  })
  status: string;

  @Prop({
    type: Number,
    required: true,
  })
  itemId: number;
}

class ShopeeCampainManualBiddingInfo {
  @Prop({
    type: Boolean,
    required: true,
  })
  enhancedCpc: boolean;

  @Prop({
    type: Array,
    required: true,
  })
  selectedKeywords: {
    keyword: string;
    status: string;
    matchType: string;
    bidPricePerClick: number;
  }[];

  @Prop({
    type: Array,
    required: true,
  })
  discoveryAdsLocations: {
    location: string;
    status: string;
    bidPrice: number;
  }[];
}

@Schema()
class ShoppeCampaignCommonInfo {
  @Prop({
    type: String,
    required: true,
  })
  adType: string;

  @Prop({
    type: String,
    required: true,
  })
  adName: string;

  @Prop({
    type: String,
    required: true,
  })
  campaignStatus: string;

  @Prop({
    type: String,
    required: true,
  })
  biddingMethod: string;

  @Prop({
    type: String,
    required: true,
  })
  campaignPlacement: string;

  @Prop({
    type: Number,
    required: true,
  })
  campaignBudget: number;

  @Prop({
    type: Object,
    required: true,
  })
  campaignDuration: {
    startTime: number;
    endTime: number;
  };

  @Prop({
    type: Array,
    required: true,
  })
  itemIdList: number[];
}

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (_, ret) => {
      return ret;
    },
  },
})
export class ShopeeCampaign extends BaseSchema<ShopeeCampaign> {
  @Prop({
    type: Number,
    required: true,
  })
  campaignId: number;

  @Prop({
    type: String,
    required: true,
  })
  adType: string;

  @Prop({
    type: Number,
    required: true,
  })
  partnerId: number;

  @Prop({
    type: ShoppeCampaignCommonInfo,
    required: true,
    default: {},
  })
  commonInfo: ShoppeCampaignCommonInfo;

  @Prop({
    type: ShopeeCampainManualBiddingInfo,
    required: true,
    default: {},
  })
  manualBiddingInfo: ShopeeCampainManualBiddingInfo;

  @Prop({
    type: ShopeeCampainAutoBiddingInfo,
    required: true,
    default: {},
  })
  autoBiddingInfo: ShopeeCampainAutoBiddingInfo;

  @Prop({
    type: ShopeeCampainAutoProductAdsInfo,
    required: true,
    default: {},
  })
  autoProductAdsInfo: ShopeeCampainAutoProductAdsInfo;

  @Prop({
    type: Object,
    required: true,
    default: {},
  })
  metadata: Record<string, any>;
}

export const ShopeeCampaignSchema =
  SchemaFactory.createForClass(ShopeeCampaign);
