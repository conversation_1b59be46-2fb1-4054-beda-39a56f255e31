import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { ShopeeAdsService } from './shopee-ads.service';
import { Public } from '../../decorators/public.decorator';

@ApiTags('shopee-ads')
@Controller('shopee-ads')
export class ShopeeAdsController {
  constructor(private readonly shopeeAdsService: ShopeeAdsService) {}

  @Get('sync-campaigns')
  @Public()
  @ApiOperation({ summary: 'Sync ads list from Shopee API' })
  async syncCampaigns(@Query('partnerId') partnerId: string) {
    return this.shopeeAdsService.getCampaignsAndUpsert(Number(partnerId));
  }

  @Get('sync-campaign-details')
  @Public()
  @ApiOperation({ summary: 'Sync ads list from Shopee API' })
  async syncCampaignDetails(@Query('partnerId') partnerId: string) {
    return this.shopeeAdsService.getCampaignDetails(Number(partnerId));
  }

  @Get('list')
  @Public()
  @ApiOperation({ summary: 'Get ads list from database' })
  async getAdsList() {
    return this.shopeeAdsService.find({ deletedAt: null });
  }
}
