import * as moment from 'moment';
import { Injectable } from '@nestjs/common';
import { ShopeeApi } from '../../../ecommerces/shopee';
import { ShopeeOrder } from './shopee-order.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { BulkWriteResult } from 'mongodb';

@Injectable()
export class ShopeeOrderService {
  constructor(
    @InjectModel(ShopeeOrder.name)
    private readonly shopeeOrderModel: Model<ShopeeOrder>,
    private readonly shopeeApi: ShopeeApi,
  ) {}

  async getOrders(query) {
    const startTime = moment('2025-01-01').startOf('day').unix(); // 01-01-2025 00:00:00
    const endTime = moment().endOf('day').unix(); // Now till end of today

    const DAY_IN_SECONDS = 86400;
    const CHUNK_SIZE = 15 * DAY_IN_SECONDS; // 15 days

    let currentFrom = startTime;
    let allOrders = [];

    while (currentFrom < endTime) {
      const currentTo = Math.min(currentFrom + CHUNK_SIZE - 1, endTime);

      const ordersFromApi = await this.shopeeApi.getOrderList({
        partnerId: query.partnerId,
        timeFrom: currentFrom,
        timeTo: currentTo,
      });

      const orders = ordersFromApi.response['order_list'] || [];

      if (orders.length > 0) {
        await this.bulkUpsert(
          orders.map((order) => ({
            orderSn: order['order_sn'],
            partnerId: query.partnerId,
          })),
        );
      }

      allOrders = allOrders.concat(orders);
      currentFrom = currentTo + 1;
    }

    return allOrders;
  }

  async getOrderDetail(query) {
    const type = query.type;
    if (type === 'all') {
      const orders = await this.shopeeOrderModel
        .find(
          {
            deletedAt: null,
          },
          {
            _id: 1,
            orderSn: 1,
          },
        )
        .skip(0)
        .limit(50);
      const orderDetails = await this.shopeeApi.getOrderDetail({
        partnerId: query.partnerId,
        orderSnList: orders.map((order) => order.orderSn),
      });
      console.log('orderDetails', orderDetails);
      return;
    }
    const orderSn = query.orderSn;
    const orderDetail = await this.shopeeApi.getOrderDetail({
      partnerId: query.partnerId,
      orderSnList: [orderSn],
      responseOptionalFields: query.responseOptionalFields,
    });
    console.log('orderDetail', orderDetail);
    return orderDetail;
  }

  async bulkUpsert(list): Promise<BulkWriteResult> {
    const bulkOps = list.map((update) => ({
      updateOne: {
        filter: { orderSn: update.orderSn },
        update: { $set: update },
        upsert: true,
      },
    }));

    const upsertData = await this.shopeeOrderModel.bulkWrite(bulkOps);
    return upsertData;
  }
}
