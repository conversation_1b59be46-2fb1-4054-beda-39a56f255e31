import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../../../utils/enums';
import { Controller, Get, Query } from '@nestjs/common';
import { description, getDescription } from '../../../../utils/descriptions';
import { ShopeeOrderService } from './shopee-order.service';
import { ApiExcludeAll } from '../../../../decorators/global-swagger-exclude.decorator';

@ApiTags(RouteName.shopeeOrder)
@Controller(RouteName.shopeeOrder)
@ApiExcludeAll()
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({
  description: description.httpStatus.unAuthorized,
})
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class ShopeeOrderController {
  constructor(private readonly shopeeOrderService: ShopeeOrderService) {}

  @Get('')
  @ApiOperation({
    summary: getDescription(
      RouteName.shopeeOrder,
      description.controller.getOne,
    ),
  })
  async getOrders(
    @Query()
    query: {
      partnerId: string;
      timeFrom: string;
      timeTo: string;
    },
  ): Promise<any> {
    return this.shopeeOrderService.getOrders(query);
  }

  @Get('details')
  @ApiOperation({
    summary: getDescription(
      RouteName.shopeeOrder,
      description.controller.getOne,
    ),
  })
  async getOrderDetails(
    @Query()
    query: {
      partnerId: string;
      orderSn: string;
      type: string;
      responseOptionalFields: string;
    },
  ): Promise<any> {
    return this.shopeeOrderService.getOrderDetail(query);
  }
}
