import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../../../shared/base/base.schema';

export type ShopeeOrderDocument = HydratedDocument<ShopeeOrder>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class ShopeeOrder extends BaseSchema<ShopeeOrder> {
  @Prop({
    type: String,
    required: true,
  })
  orderSn: string;

  @Prop({
    type: Number,
    required: true,
  })
  partnerId: number;

  @Prop({
    type: Number,
    required: false,
  })
  createTime: number;

  @Prop({
    type: Number,
    required: false,
  })
  updateTime: number;

  @Prop({
    type: Object,
    required: false,
  })
  recipientAddress: {
    name: string;
    phone: string;
    town: string;
    district: string;
    city: string;
    state: string;
    fullAddress: string;
  };

  @Prop({
    type: String,
    required: false,
  })
  note: string;

  @Prop({
    type: Object,
    required: false,
  })
  jsonDetail: Object;
}

export const ShopeeOrderSchema = SchemaFactory.createForClass(ShopeeOrder);
