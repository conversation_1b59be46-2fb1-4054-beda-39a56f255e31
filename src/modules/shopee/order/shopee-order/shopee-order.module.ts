import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { ShopeeOrder, ShopeeOrderSchema } from './shopee-order.schema';
import { ShopeeOrderService } from './shopee-order.service';
import { ShopeeApi } from '../../../ecommerces/shopee';
import { RedisModule } from '../../../platforms/storages/storages/redis/redis.module';
import { ShopeeOrderController } from './shopee-order.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ShopeeOrder.name,
        schema: ShopeeOrderSchema,
        collection: 'shopee-orders',
      },
    ]),
    RedisModule,
  ],
  providers: [ShopeeOrderService, ShopeeApi],
  controllers: [ShopeeOrderController],
  exports: [MongooseModule, ShopeeOrderService],
})
export class ShopeeOrderModule {}
