import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { Controller, Get, Query } from '@nestjs/common';
import { description, getDescription } from '../../utils/descriptions';
import { ShopeeItemCommentService } from './shopee-comment.service';
import { ApiExcludeAll } from '../../decorators/global-swagger-exclude.decorator';

@ApiTags(RouteName.shopeeItemComment)
@Controller(RouteName.shopeeItemComment)
@ApiExcludeAll()
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class ShopeeItemCommentController {
  constructor(
    private readonly shopeeItemCommentService: ShopeeItemCommentService,
  ) {}

  @Get('')
  @ApiOperation({
    summary: getDescription(RouteName.auths, description.controller.getOne),
  })
  async getItemCommentsAndUpsertFromApi(
    @Query() query: { partnerId: string },
  ): Promise<any> {
    return this.shopeeItemCommentService.getItemCommentsAndUpsertFromApi(
      Number(query.partnerId),
    );
  }
}
