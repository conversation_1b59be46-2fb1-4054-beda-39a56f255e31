import { FilterQuery, Model, QueryOptions } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BulkWriteResult } from 'mongodb';
import { ShopeeApiService } from '../../api/shopee-api/shopee-api-service';
import { ShopeeItemComment } from './shopee-comment.schema';
import { ShopeeItemService } from '../../item/shopee-item/shopee-item.service';
import { wait } from '../../../../utils/time';
import * as moment from 'moment';

@Injectable()
export class ShopeeItemCommentService {
  constructor(
    @InjectModel(ShopeeItemComment.name)
    private readonly shopeeItemComment: Model<ShopeeItemComment>,
    private readonly shopeeApiService: ShopeeApiService,
    private readonly shopeeItemService: ShopeeItemService,
  ) {}

  find(
    filter?: FilterQuery<ShopeeItemComment>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<ShopeeItemComment[]> {
    return this.shopeeItemComment.find(filter, projection, options);
  }

  async getItemCommentByDay(n: number) {
    const nDaysAgo = moment().subtract(n, 'days').unix();
    const now = moment().unix();

    return this.shopeeItemComment.find({
      createTime: { $gte: nDaysAgo, $lte: now },
    });
  }

  async getLastItemCommentFromApi(
    ratingStar: number[],
    itemId: number,
    lastTime: number,
  ) {
    const itemComments = await this.shopeeItemComment.find({
      itemId: itemId,
      ratingStar: {
        $in: ratingStar,
      },
      createTime: {
        $gt: lastTime,
      },
    });
    return itemComments;
  }

  async getItemCommentsAndUpsertFromApi(partnerId: number) {
    const page = 1;
    const limit = 1;
    const skip = (page - 1) * limit;

    const items = await this.shopeeItemService.find(
      {
        deletedAt: null,
      },
      {
        _id: 1,
        itemId: 1,
        itemName: 1,
      },
      // { limit: limit, skip: skip },
    );
    const pageSize = 50;

    for (const item of items) {
      let commentId = 0; // get all comments
      let pageCursor = 0;
      while (true) {
        const resultFromApi = await this.shopeeApiService.getItemComment(
          item.itemId,
          commentId,
          pageSize,
          pageCursor,
          partnerId,
        );
        const itemCommentListFromApi =
          resultFromApi.response['item_comment_list'];
        if (itemCommentListFromApi.length) {
          const itemCommentList = itemCommentListFromApi.map(
            (itemCommentFromApi) => ({
              commentId: itemCommentFromApi['comment_id'],
              comment: itemCommentFromApi['comment'],
              buyerUsername: itemCommentFromApi['buyer_username'],
              orderSn: itemCommentFromApi['order_sn'],
              itemId: itemCommentFromApi['item_id'],
              itemName: item.itemName,
              createTime: itemCommentFromApi['create_time'],
              createTimeUnix: moment
                .unix(itemCommentFromApi['create_time'])
                .format('YYYY-MM-DD HH:mm:ss'),
              ratingStar: itemCommentFromApi['rating_star'],
              editable: itemCommentFromApi['editable'],
              jsonDetail: itemCommentFromApi,
            }),
          );
          await this.bulkUpsert(itemCommentList);
        }
        pageCursor += pageSize;
        if (!resultFromApi.response['more']) {
          break;
        }
        await wait(1000);
      }
    }
  }

  async getLastItemCommentsAndUpsertFromApi(partnerId: number) {
    const page = 1;
    const limit = 1;
    const skip = (page - 1) * limit;

    const items = await this.shopeeItemService.find(
      {
        deletedAt: null,
      },
      {
        _id: 1,
        itemId: 1,
        itemName: 1,
      },
      // { limit: limit, skip: skip },
    );
    const pageSize = 50; // get 50 item

    for (const item of items) {
      let commentId = 0; // get all comments
      let pageCursor = 0; // cursor 0 get lastest comment
      const resultFromApi = await this.shopeeApiService.getItemComment(
        item.itemId,
        commentId,
        pageSize,
        pageCursor,
        partnerId,
      );

      const itemCommentListFromApi =
        resultFromApi.response['item_comment_list'];

      if (itemCommentListFromApi.length) {
        const itemCommentList = itemCommentListFromApi.map(
          (itemCommentFromApi) => ({
            commentId: itemCommentFromApi['comment_id'],
            comment: itemCommentFromApi['comment'],
            buyerUsername: itemCommentFromApi['buyer_username'],
            orderSn: itemCommentFromApi['order_sn'],
            itemId: itemCommentFromApi['item_id'],
            itemName: item.itemName,
            createTime: itemCommentFromApi['create_time'],
            createTimeUnix: moment
              .unix(itemCommentFromApi['create_time'])
              .format('YYYY-MM-DD HH:mm:ss'),
            ratingStar: itemCommentFromApi['rating_star'],
            editable: itemCommentFromApi['editable'],
            jsonDetail: itemCommentFromApi,
            partnerId,
          }),
        );
        await this.bulkUpsert(itemCommentList);
      }
    }
  }

  async bulkUpsert(list): Promise<BulkWriteResult> {
    const bulkOps = list.map((update) => ({
      updateOne: {
        filter: { commentId: update.commentId },
        update: { $set: update },
        upsert: true,
      },
    }));

    const upsertData = await this.shopeeItemComment.bulkWrite(bulkOps);
    return upsertData;
  }
}
