import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../../../shared/base/base.schema';

export type ShopeeItemCommentDocument = HydratedDocument<ShopeeItemComment>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class ShopeeItemComment extends BaseSchema<ShopeeItemComment> {
  @Prop({
    type: Number,
    required: true,
  })
  commentId: number;

  @Prop({
    type: String,
    required: true,
  })
  comment: string;

  @Prop({
    type: String,
    required: true,
  })
  buyerUsername: string;

  @Prop({
    type: String,
    required: true,
  })
  orderSn: string;

  @Prop({
    type: Number,
    required: true,
  })
  itemId: number;

  @Prop({
    type: String,
    required: false,
  })
  itemName: String;

  @Prop({
    type: Number,
    required: true,
  })
  createTime: number;

  @Prop({
    type: String,
    required: true,
  })
  createTimeUnix: string;

  @Prop({
    type: Number,
    required: true,
  })
  ratingStar: number;

  @Prop({
    type: String,
    required: true,
  })
  editable: string;

  @Prop({
    type: Object,
    required: false,
  })
  jsonDetail: Object;

  @Prop({
    type: Number,
    required: true,
  })
  partnerId: number;
}

export const ShopeeItemCommentSchema =
  SchemaFactory.createForClass(ShopeeItemComment);
