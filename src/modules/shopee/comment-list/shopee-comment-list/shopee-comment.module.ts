import { ShopeeApiModule } from '../../api/shopee-api/shopee-api.module';
import { Module } from '@nestjs/common';
import { ShopeeConfigurationModule } from '../../configuration/shopee-configuration/shopee-configuration.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ShopeeItemCommentController } from './shopee-comment.controller';
import {
  ShopeeItemComment,
  ShopeeItemCommentSchema,
} from './shopee-comment.schema';
import { ShopeeItemCommentService } from './shopee-comment.service';
import { ShopeeItemModule } from '../../item/shopee-item/shopee-item.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ShopeeItemComment.name,
        schema: ShopeeItemCommentSchema,
        collection: 'shopee-item-comments',
      },
    ]),
    ShopeeConfigurationModule,
    ShopeeItemModule,
    ShopeeApiModule,
  ],
  controllers: [ShopeeItemCommentController],
  providers: [ShopeeItemCommentService],
  exports: [MongooseModule, ShopeeItemCommentService],
})
export class ShopeeItemCommentModule {}
