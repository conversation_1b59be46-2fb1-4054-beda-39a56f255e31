import { FilterQuery, Model, QueryOptions } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ShopeeItem } from './shopee-item.schema';
import { BulkWriteResult } from 'mongodb';
import { ShopeeApiService } from '../../api/shopee-api/shopee-api-service';

@Injectable()
export class ShopeeItemService {
  constructor(
    @InjectModel(ShopeeItem.name)
    private shopeeItem: Model<ShopeeItem>,
    private readonly shopeeApiService: ShopeeApiService,
  ) {}

  find(
    filter?: FilterQuery<ShopeeItem>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<ShopeeItem[]> {
    return this.shopeeItem.find(filter, projection, options);
  }

  async getDetailItemsAndUpsertFromApi(partnerId: number) {
    const page = 1;
    const limit = 10000000;
    const skip = (page - 1) * limit;

    const items = await this.shopeeItem.find({}, {}, { limit, skip });

    for (const item of items) {
      const resultFromApi = await this.shopeeApiService.getItem(
        String(item.itemId),
        partnerId,
      );
      const [itemFromApi] = resultFromApi.response['item_list'];
      if (itemFromApi) {
        await this.shopeeItem.updateOne(
          {
            _id: item._id,
          },
          {
            $set: {
              itemName: itemFromApi['item_name'],
              itemSku: itemFromApi['item_sku'],
              createTime: itemFromApi['create_time'],
              updateTime: itemFromApi['update_time'],
              jsonDetail: itemFromApi,
            },
          },
        );
      }
    }
  }

  async getItemsAndUpsertFromApi(partnerId: number) {
    let offset = 0;

    while (true) {
      const resultFromApi = await this.shopeeApiService.getListItem(
        offset,
        partnerId,
      );
      const items = resultFromApi.response.item;

      if (items.length) {
        const itemsConverted = items.map((item) => ({
          itemId: item['item_id'],
          itemStatus: item['item_status'],
          updateTime: item['update_time'],
          tag: item['tag'],
          partnerId,
        }));
        await this.bulkUpsert(itemsConverted);
      }
      if (!resultFromApi.response['has_next_page']) {
        return;
      }
      offset += 10;
    }
  }

  async bulkUpsert(list): Promise<BulkWriteResult> {
    const bulkOps = list.map((update) => ({
      updateOne: {
        filter: { itemId: update.itemId },
        update: { $set: update },
        upsert: true,
      },
    }));

    const upsertData = await this.shopeeItem.bulkWrite(bulkOps);
    return upsertData;
  }
}
