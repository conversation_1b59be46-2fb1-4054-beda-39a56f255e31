import {
  ApiBadRequestResponse,
  ApiExcludeEndpoint,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../../../utils/enums';
import { Controller, Get, Query } from '@nestjs/common';
import { description, getDescription } from '../../../../utils/descriptions';
import { ShopeeItemService } from './shopee-item.service';
import { ApiExcludeAll } from '../../../../decorators/global-swagger-exclude.decorator';

@ApiTags(RouteName.shopeeItem)
@Controller(RouteName.shopeeItem)
@ApiExcludeAll()
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class ShopeeItemController {
  constructor(private readonly shopeeItemService: ShopeeItemService) {}

  @Get('')
  @ApiOperation({
    summary: getDescription(RouteName.auths, description.controller.getOne),
  })
  async getItemsAndUpsertFromApi(
    @Query() query: { partnerId: string },
  ): Promise<any> {
    return this.shopeeItemService.getItemsAndUpsertFromApi(
      Number(query.partnerId),
    );
  }

  @Get('details-all')
  @ApiOperation({
    summary: getDescription(RouteName.auths, description.controller.getOne),
  })
  async getDetailItemsAndUpsertFromApi(
    @Query() query: { partnerId: string },
  ): Promise<any> {
    return this.shopeeItemService.getDetailItemsAndUpsertFromApi(
      Number(query.partnerId),
    );
  }
}
