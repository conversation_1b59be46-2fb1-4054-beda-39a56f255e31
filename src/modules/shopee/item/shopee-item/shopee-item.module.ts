import { ShopeeApiModule } from '../../api/shopee-api/shopee-api.module';
import { Module } from '@nestjs/common';
import { ShopeeItemService } from './shopee-item.service';
import { ShopeeConfigurationModule } from '../../configuration/shopee-configuration/shopee-configuration.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ShopeeItem, ShopeeItemSchema } from './shopee-item.schema';
import { ShopeeItemController } from './shopee-item.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ShopeeItem.name,
        schema: ShopeeItemSchema,
        collection: 'shopee-items',
      },
    ]),
    ShopeeApiModule,
  ],
  controllers: [ShopeeItemController],
  providers: [ShopeeItemService],
  exports: [MongooseModule, ShopeeItemService],
})
export class ShopeeItemModule {}
