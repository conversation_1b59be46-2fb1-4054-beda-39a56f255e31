import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../../../shared/base/base.schema';

export type ShopeeItemDocument = HydratedDocument<ShopeeItem>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class ShopeeItem extends BaseSchema<ShopeeItem> {
  @Prop({
    type: Number,
    required: true,
  })
  itemId: number;

  @Prop({
    type: String,
    required: false,
  })
  itemName: string;

  @Prop({
    type: String,
    required: false,
  })
  itemSku: string;

  @Prop({
    type: String,
    required: true,
  })
  itemStatus: string;

  @Prop({
    type: Object,
    required: true,
  })
  tag: {
    kit: boolean;
  };

  @Prop({
    type: Number,
    required: false,
  })
  createTime: number;

  @Prop({
    type: Number,
    required: false,
  })
  updateTime: number;

  @Prop({
    type: Object,
    required: false,
  })
  jsonDetail: Object;

  @Prop({
    type: Number,
    required: true,
  })
  partnerId: number;
}

export const ShopeeItemSchema = SchemaFactory.createForClass(ShopeeItem);
