import { LarkBaseType } from './../types/type';
import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { LarkApi } from '../utils/enum';
import { convertToSnakeCase } from '../../../../utils/case-converter';

@Injectable()
export class LarkBaseApi {
  private readonly baseUrl = 'https://open.larksuite.com/open-apis';

  private getCommonHeader(accessToken) {
    return {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    };
  }

  async getFields<T>(options: LarkBaseType, params: T) {
    const url = LarkApi.getField
      .replace('{appToken}', options.appToken)
      .replace('{tableId}', options.tableId);

    const response = await axios({
      url: `${this.baseUrl}${url}`,
      method: 'get',
      headers: this.getCommonHeader(options.accessToken),
      params: params ? convertToSnakeCase(params) : {},
    }).catch((err) => {
      console.log(err);
      throw err;
    });
    return response.data;
  }

  async createField<T>(options: LarkBaseType, data: T) {
    const url = LarkApi.createField
      .replace('{appToken}', options.appToken)
      .replace('{tableId}', options.tableId);

    const response = await axios({
      url: `${this.baseUrl}${url}`,
      method: 'post',
      headers: this.getCommonHeader(options.accessToken),
      data: data ? convertToSnakeCase(data) : {},
    }).catch((err) => {
      console.log(err);
      throw err;
    });

    return response.data;
  }

  async createRecords<T>(options: LarkBaseType, data: T) {
    const url = LarkApi.createBatchRecord
      .replace('{appToken}', options.appToken)
      .replace('{tableId}', options.tableId);

    const response = await axios({
      url: `${this.baseUrl}${url}`,
      method: 'post',
      headers: this.getCommonHeader(options.accessToken),
      data: data,
    }).catch((err) => {
      console.error('[Lark Error]', err?.response?.data || err);
      throw err;
    });
    return response.data;
  }

  async deleteRecords<T>(options: LarkBaseType, data: T) {
    const url = LarkApi.deleteBatchRecord
      .replace('{appToken}', options.appToken)
      .replace('{tableId}', options.tableId);

    const response = await axios({
      url: `${this.baseUrl}${url}`,
      method: 'post',
      headers: this.getCommonHeader(options.accessToken),
      data: data,
    }).catch((err) => {
      console.error('[Lark Error]', err?.response?.data || err);
      throw err;
    });
    console.log('response', response);
    return response.data;
  }
}
