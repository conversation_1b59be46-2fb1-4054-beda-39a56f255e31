// redis.service.ts
import { Injectable } from '@nestjs/common';
import { RedisLoaderService } from './redis-loader.service';

@Injectable()
export class RedisService {
  constructor(private readonly redisLoader: RedisLoaderService) {}

  async setValue(key: string, value: any, ttl?: number) {
    const client = this.redisLoader.getClient();

    const val = typeof value === 'string' ? value : JSON.stringify(value);
    if (ttl) {
      await client.set(key, val, 'EX', ttl);
    } else {
      await client.set(key, val);
    }
  }

  async getValue<T = any>(key: string): Promise<T | null> {
    const client = this.redisLoader.getClient();
    const result = await client.get(key);
    if (!result) return null;

    try {
      return JSON.parse(result) as T;
    } catch {
      return result as unknown as T;
    }
  }

  async publish(channel: string, data: any) {
    const client = this.redisLoader.getClient();
    return client.publish(channel, JSON.stringify(data));
  }

  async subscribe(channel: string, callback: (data: any) => void) {
    const subClient = this.redisLoader.getClient().duplicate();
    await subClient.subscribe(channel);
    subClient.on('message', (_channel, message) => {
      try {
        callback(JSON.parse(message));
      } catch {
        callback(message);
      }
    });
  }
}
