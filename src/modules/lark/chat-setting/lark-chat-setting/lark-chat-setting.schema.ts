import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';

export type LarkChatSettingDocument = HydratedDocument<LarkChatSetting>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class LarkChatSetting extends BaseSchema<LarkChatSetting> {
  @Prop({
    type: String,
    required: true,
  })
  chatId: string;

  @Prop({
    type: String,
    required: false,
  })
  appId: string;

  @Prop({
    type: [Number],
    required: true,
    validate: [
      (val: number[]) => val.every((n) => n >= 0 && n <= 5),
      'Invalid rating star',
    ],
    default: [],
  })
  ratingStar: number[];
}

export const LarkChatSettingSchema =
  SchemaFactory.createForClass(LarkChatSetting);
