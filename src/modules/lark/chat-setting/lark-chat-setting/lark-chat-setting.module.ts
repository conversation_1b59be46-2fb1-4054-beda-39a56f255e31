import { Module } from '@nestjs/common';
import { LarkChatSettingService } from './lark-chat-setting.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  LarkChatSetting,
  LarkChatSettingSchema,
} from './lark-chat-setting.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: LarkChatSetting.name,
        schema: LarkChatSettingSchema,
        collection: 'lark-chat-settings',
      },
    ]),
  ],
  providers: [LarkChatSettingService],
  exports: [MongooseModule, LarkChatSettingService],
})
export class LarkChatSettingModule {}
