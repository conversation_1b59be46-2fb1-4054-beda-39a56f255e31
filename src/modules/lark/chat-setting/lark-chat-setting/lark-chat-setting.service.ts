import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, QueryOptions } from 'mongoose';
import { LarkChatSetting } from './lark-chat-setting.schema';

@Injectable()
export class LarkChatSettingService {
  constructor(
    @InjectModel(LarkChatSetting.name)
    private larkChatSetting: Model<LarkChatSetting>,
  ) {}

  async upsert(data: { appId: string; chatId: string; deletedAt: Date }) {
    return this.larkChatSetting.updateOne(
      { appId: data.appId, chatId: data.chatId },
      { $set: data },
      { upsert: true },
    );
  }

  find(
    filter?: FilterQuery<LarkChatSetting>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkChatSetting[]> {
    return this.larkChatSetting.find(filter, projection, options);
  }

  findOne(
    filter?: FilterQuery<LarkChatSetting>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkChatSetting> {
    return this.larkChatSetting.findOne(filter, projection, options);
  }

  async create(data): Promise<LarkChatSetting> {
    return this.larkChatSetting.create(data);
  }

  updateByChatId(chatId: string) {
    return this.larkChatSetting.updateOne(
      {
        chatId: chatId,
        deletedAt: null,
      },
      {
        $set: {
          deletedAt: new Date(),
        },
      },
    );
  }
}
