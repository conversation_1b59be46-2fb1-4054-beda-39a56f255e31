import { DynamicModule, Module } from '@nestjs/common';
import { LarkApiController } from './lark.controller';
import { LarkService } from './lark.service';
import { BotLark } from '../bot/lark';
import { Model } from 'mongoose';
import { LarkApp } from '../lark-app/lark-app.schema';
import { AppType } from '../../utils/enums';
import { getModelToken } from '@nestjs/mongoose';
import { LarkAppModule } from '../lark-app/lark-app.module';
import { LarkChatSettingModule } from '../lark-chat-setting/lark-chat-setting.module';
import { ShopeeItemCommentModule } from '../shopee-comment-list/shopee-comment.module';
import { LarkNotificationModule } from '../lark-notification/lark-notification.module';
import { ShopeeItemModule } from '../shopee-item/shopee-item.module';
import { LarkBaseApi } from '../platforms/lark/api/base';
import { LarkBaseTableModule } from '../lark-base-table/lark-base-table.module';
import { RedisModule } from '../storages/redis/redis.module';

@Module({
  imports: [
    LarkAppModule,
    LarkChatSettingModule,
    LarkAppModule,
    ShopeeItemCommentModule,
    ShopeeItemModule,
    LarkNotificationModule,
    RedisModule,
  ],
  controllers: [LarkApiController],
  providers: [LarkService, BotLark],
  exports: [LarkService],
})
export class LarkModule {}
