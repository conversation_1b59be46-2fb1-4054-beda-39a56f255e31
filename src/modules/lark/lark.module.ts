import { DynamicModule, Module } from '@nestjs/common';
import { LarkApiController } from './lark.controller';
import { LarkService } from './lark.service';
import { BotLark } from '../bot/lark';
import { Model } from 'mongoose';
import { LarkApp } from './app/lark-app/lark-app.schema';
import { AppType } from '../../utils/enums';
import { getModelToken } from '@nestjs/mongoose';
import { LarkAppModule } from './app/lark-app/lark-app.module';
import { LarkChatSettingModule } from './chat-setting/lark-chat-setting/lark-chat-setting.module';
import { ShopeeItemCommentModule } from '../shopee/comment-list/shopee-comment-list/shopee-comment.module';
import { LarkNotificationModule } from './notification/lark-notification/lark-notification.module';
import { ShopeeItemModule } from '../shopee/item/shopee-item/shopee-item.module';
import { Lark<PERSON>ase<PERSON>pi } from '../platforms/lark/api/base';
import { LarkBaseTableModule } from './base-table/lark-base-table/lark-base-table.module';
import { RedisModule } from '../platforms/storages/storages/redis/redis.module';

@Module({
  imports: [
    LarkAppModule,
    LarkChatSettingModule,
    LarkAppModule,
    ShopeeItemCommentModule,
    ShopeeItemModule,
    LarkNotificationModule,
    RedisModule,
  ],
  controllers: [LarkApiController],
  providers: [LarkService, BotLark],
  exports: [LarkService],
})
export class LarkModule {}
