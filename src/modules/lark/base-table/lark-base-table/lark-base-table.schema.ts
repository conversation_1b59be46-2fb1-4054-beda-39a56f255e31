import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../../../shared/base/base.schema';

export type LarkBaseTableDocument = HydratedDocument<LarkBaseTable>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class LarkBaseTable extends BaseSchema<LarkBaseTable> {
  @Prop({
    type: String,
    required: true,
  })
  tableId: string;

  @Prop({
    type: String,
    required: true,
  })
  fieldCode: string;

  @Prop({
    type: String,
    required: true,
  })
  fieldId: string;

  @Prop({
    type: String,
    required: true,
  })
  fieldName: string;

  @Prop({
    type: String,
    required: true,
    default: 'string',
  })
  fieldFormat: string;
}

export const LarkBaseTableSchema = SchemaFactory.createForClass(LarkBaseTable);
