import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LarkBaseTable, LarkBaseTableSchema } from './lark-base-table.schema';
import { LarkBaseTableService } from './lark-base-table.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: LarkBaseTable.name,
        schema: LarkBaseTableSchema,
        collection: 'lark-base-tables',
      },
    ]),
  ],
  providers: [LarkBaseTableService],
  exports: [MongooseModule, LarkBaseTableService],
})
export class LarkBaseTableModule {}
