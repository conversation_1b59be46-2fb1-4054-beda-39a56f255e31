import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, QueryOptions } from 'mongoose';
import { LarkBaseTable } from './lark-base-table.schema';

@Injectable()
export class LarkBaseTableService {
  constructor(
    @InjectModel(LarkBaseTable.name)
    private larBaseTable: Model<LarkBaseTable>,
  ) {}

  async upsert(data: Partial<LarkBaseTable>) {
    return this.larBaseTable.updateOne(
      { fieldCode: data.fieldCode },
      { $set: data },
      { upsert: true },
    );
  }

  find(
    filter?: FilterQuery<LarkBaseTable>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkBaseTable[]> {
    return this.larBaseTable.find(filter, projection, options);
  }

  findOne(
    filter?: FilterQuery<LarkBaseTable>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkBaseTable> {
    return this.larBaseTable.findOne(filter, projection, options);
  }

  async create(data): Promise<LarkBaseTable> {
    return this.larBaseTable.create(data);
  }
}
