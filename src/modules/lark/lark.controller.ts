import {
  ApiBadRequestResponse,
  ApiExcludeController,
  ApiExcludeEndpoint,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { description, getDescription } from '../../utils/descriptions';
import { LarkService } from './lark.service';
import { Public } from '../../decorators/public.decorator';

@ApiTags(RouteName.larks)
@Controller(RouteName.larks)
@ApiExcludeController()
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class LarkApiController {
  constructor(private readonly larksService: LarkService) {}

  @Public()
  @Post('/:appId/webhook')
  @ApiOperation({
    summary: getDescription(RouteName.larks, description.controller.webhook),
  })
  async handleWebhook(
    @Param('appId') appId: string,
    @Body() body: any,
  ): Promise<any> {
    return this.larksService.handleWebhook(body, appId);
  }

  @Get('send-message')
  @ApiExcludeEndpoint()
  @ApiOperation({
    summary: getDescription(RouteName.larks, description.controller.getOne),
  })
  async sendMessageToGroup(
    @Query() query: { chatId: string; text: string; appId: string },
  ): Promise<any> {
    return this.larksService.sendMessageToGroup(query);
  }

  @Get('test-send-message')
  @ApiExcludeEndpoint()
  @ApiOperation({
    summary: getDescription(RouteName.larks, description.controller.getOne),
  })
  async testSendMessageToGroup(@Query() query: any): Promise<any> {
    return this.larksService.testSendMessageToGroup(query);
  }
}
