import { Module } from '@nestjs/common';
import { LarkAppService } from './lark-app.service';
import { MongooseModule } from '@nestjs/mongoose';
import { LarkApp, LarkAppSchema } from './lark-app.schema';
import { LarkAppLoaderService } from './lark-app-loader.service';
import { RedisModule } from '../storages/redis/redis.module';
import { BotLark } from '../bot/lark';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: LarkApp.name,
        schema: LarkAppSchema,
        collection: 'lark-apps',
      },
    ]),
    RedisModule,
  ],
  providers: [LarkAppLoaderService, LarkAppService, BotLark],
  exports: [MongooseModule, LarkAppService],
})
export class LarkAppModule {}
