import { Injectable, OnModuleInit } from '@nestjs/common';
import { LarkAppService } from './lark-app.service';
import { AppType, Environment, RedisKey } from '../../../../utils/enums';
import { RedisService } from '../../../platforms/storages/storages/redis/redis.service';
import { BotLark } from '../../../bot/lark';
import { showLarkConfigurationByTable } from '../../../../utils/logs';

@Injectable()
export class LarkAppLoaderService implements OnModuleInit {
  constructor(
    private readonly larkAppService: LarkAppService,
    private readonly redisService: RedisService,
    private readonly botLark: BotLark,
  ) {}

  async loadConfiguration() {
    const larkEnv = process.env.LARK_ENV;
    const configurations = await this.larkAppService.find({
      appType: AppType.notifyFeedBackUser,
      deletedAt: null,
      env: larkEnv,
    });

    console.log(`Set Lark Configuration from Redis: ${larkEnv}`);

    for (const config of configurations) {
      if (!config.tenantAccessToken) {
        const response = await this.botLark.getAccessToken(config);
        const newConfig = {
          ...(config as any).toJSON(),
          tenantAccessToken: response['tenant_access_token'],
          expire: response['expire'],
        };
        await this.larkAppService.update((config as any)._id, {
          tenantAccessToken: response['tenant_access_token'],
          expire: response['expire'],
        });
        await this.redisService.setValue(
          `${RedisKey.lark}_${newConfig.appId}`,
          newConfig,
        );
      } else {
        await this.redisService.setValue(
          `${RedisKey.lark}_${config.appId}`,
          config,
        );
      }
      showLarkConfigurationByTable(config);
    }
  }

  async onModuleInit() {
    const larkAppConfigSandbox = await this.larkAppService.findOne({
      appType: AppType.notifyFeedBackUser,
      deletedAt: null,
      env: Environment.sandBox,
    });
    if (!larkAppConfigSandbox) {
      await this.larkAppService.create({
        baseUrl: 'https://open.larksuite.com',
        appId: 'cli_a767a3ddfbf91028',
        appSecret: 'M2YqnI0NRhoTGAg6IgIjgd6Oft3Ny8r2',
        appType: AppType.notifyFeedBackUser,
        env: Environment.sandBox,
      });
    }

    const larkAppConfigLive = await this.larkAppService.findOne({
      appType: AppType.notifyFeedBackUser,
      deletedAt: null,
      env: Environment.live,
    });
    if (!larkAppConfigLive) {
      await this.larkAppService.create({
        baseUrl: 'https://open.larksuite.com',
        appId: 'cli_a77a07c29bf85029',
        appSecret: '37nCmD4obFg3IoPmYcsmObctAbFAMCq8',
        appType: AppType.notifyFeedBackUser,
        env: Environment.live,
      });
    }
    await this.loadConfiguration();
  }
}
