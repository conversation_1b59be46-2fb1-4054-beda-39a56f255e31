import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { AppType, Environment } from '../../utils/enums';

export type LarkAppDocument = HydratedDocument<LarkApp>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class LarkApp extends BaseSchema<LarkApp> {
  @Prop({
    type: String,
    required: true,
  })
  baseUrl: string;

  @Prop({
    type: String,
    required: true,
  })
  appId: string;

  @Prop({
    type: String,
    required: true,
  })
  appSecret: string;

  @Prop({
    type: String,
    required: false,
  })
  tenantAccessToken: string;

  @Prop({
    type: Number,
    required: false,
  })
  expire: number;

  @Prop({
    type: String,
    required: true,
    enum: AppType,
  })
  appType: string;

  @Prop({
    type: String,
    enum: Object.values(Environment),
    required: true,
  })
  env: string;
}

export const LarkAppSchema = SchemaFactory.createForClass(LarkApp);
