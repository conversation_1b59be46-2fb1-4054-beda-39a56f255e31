import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { LarkApp } from './lark-app.schema';
import { FilterQuery, Model, QueryOptions, Types } from 'mongoose';
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class LarkAppService {
  constructor(
    @InjectModel(LarkApp.name)
    private larkApp: Model<LarkApp>,
  ) {}

  @OnEvent('larkApp.update')
  async handleLarkAppUpdate(payload) {
    const config = await this.findOne({
      appId: payload.appId,
      deletedAt: null,
      env: payload.env,
    });
    if (config) {
      await this.update(config._id, {
        tenantAccessToken: payload.tenantAccessToken,
        expire: payload.expireIn,
      });
    }
  }

  find(
    filter?: FilterQuery<LarkApp>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkApp[]> {
    return this.larkApp.find(filter, projection, options);
  }

  findOne(
    filter?: FilterQuery<LarkApp>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkApp> {
    return this.larkApp.findOne(filter, projection, options);
  }

  async create(data): Promise<LarkApp> {
    return this.larkApp.create(data);
  }

  async update(id: string, data) {
    return this.larkApp.updateOne(
      {
        _id: new Types.ObjectId(id),
      },
      {
        $set: data,
      },
    );
  }
}
