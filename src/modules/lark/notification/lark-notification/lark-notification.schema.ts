import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../shared/base/base.schema';
import { NotificationStatus, NotificationType } from '../../utils/enums';

export type LarkNotificationDocument = HydratedDocument<LarkNotification>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class LarkNotification extends BaseSchema<LarkNotification> {
  @Prop({
    type: String,
    required: false,
  })
  chatId: string;

  @Prop({
    type: String,
    required: false,
  })
  tableId: string;

  @Prop({
    type: String,
    required: true,
  })
  key: string;

  @Prop({
    type: String,
    required: true,
  })
  value: string;

  @Prop({
    type: Number,
    required: true,
  })
  createTime: number;

  @Prop({
    type: String,
    required: true,
    enum: NotificationStatus,
  })
  status: string;

  @Prop({
    type: String,
    required: true,
    enum: NotificationType,
  })
  type: string;

  @Prop({
    type: Object,
    required: false,
  })
  contentJson: Object;
}

export const LarkNotificationSchema =
  SchemaFactory.createForClass(LarkNotification);
