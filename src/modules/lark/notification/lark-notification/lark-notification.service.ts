import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, QueryOptions } from 'mongoose';
import { LarkNotification } from './lark-notification.schema';
import * as moment from 'moment';

@Injectable()
export class LarkNotificationService {
  constructor(
    @InjectModel(LarkNotification.name)
    private larkNotificationSetting: Model<LarkNotification>,
  ) {}

  async getLastCreateTimeItem(filter?: FilterQuery<LarkNotification>) {
    const notification = await this.larkNotificationSetting
      .findOne(filter)
      .sort({ createTime: -1 });
    return notification
      ? notification.createTime
      : moment('04-04-2025', 'DD-MM-YYYY').unix();
  }

  async upsert(data: { appId: string; chatId: string; deletedAt: Date }) {
    return this.larkNotificationSetting.updateOne(
      { appId: data.appId, chatId: data.chatId },
      { $set: data },
      { upsert: true },
    );
  }

  find(
    filter?: FilterQuery<LarkNotification>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkNotification[]> {
    return this.larkNotificationSetting.find(filter, projection, options);
  }

  findOne(
    filter?: FilterQuery<LarkNotification>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkNotification> {
    return this.larkNotificationSetting.findOne(filter, projection, options);
  }

  async create(data): Promise<LarkNotification> {
    return this.larkNotificationSetting.create(data);
  }
}
