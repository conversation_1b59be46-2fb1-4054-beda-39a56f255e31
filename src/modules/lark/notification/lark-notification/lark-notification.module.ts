import { Module } from '@nestjs/common';
import { LarkNotificationService } from './lark-notification.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  LarkNotification,
  LarkNotificationSchema,
} from './lark-notification.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: LarkNotification.name,
        schema: LarkNotificationSchema,
        collection: 'lark-notifications',
      },
    ]),
  ],
  providers: [LarkNotificationService],
  exports: [MongooseModule, LarkNotificationService],
})
export class LarkNotificationModule {}
