import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, QueryOptions } from 'mongoose';
import { LarkBaseSetting } from './lark-base-setting.schema';

@Injectable()
export class LarkBaseSettingService {
  constructor(
    @InjectModel(LarkBaseSetting.name)
    private larkChatSetting: Model<LarkBaseSetting>,
  ) {}

  async upsert(data: { appId: string; chatId: string; deletedAt: Date }) {
    return this.larkChatSetting.updateOne(
      { appId: data.appId, chatId: data.chatId },
      { $set: data },
      { upsert: true },
    );
  }

  find(
    filter?: FilterQuery<LarkBaseSetting>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkBaseSetting[]> {
    return this.larkChatSetting.find(filter, projection, options);
  }

  findOne(
    filter?: FilterQuery<LarkBaseSetting>,
    projection?: any | null,
    options?: QueryOptions | null,
  ): Promise<LarkBaseSetting> {
    return this.larkChatSetting.findOne(filter, projection, options);
  }

  async create(data): Promise<LarkBaseSetting> {
    const found = await this.findOne({
      appId: data.appId,
      appToken: data.appToken,
      tableId: data.tableId,
    });
    if (found) {
      return found;
    }

    return this.larkChatSetting.create(data);
  }
}
