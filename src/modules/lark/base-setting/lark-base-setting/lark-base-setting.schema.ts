import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseSchema } from '../../../../shared/base/base.schema';
import { LarkBaseTableName } from '../../../../utils/enums';

export type LarkBaseSettingDocument = HydratedDocument<LarkBaseSetting>;

@Schema({
  versionKey: false,
  virtuals: true,
  toJSON: {
    getters: true,
    transform: function (_, ret) {
      delete ret._id;
    },
  },
})
export class LarkBaseSetting extends BaseSchema<LarkBaseSetting> {
  @Prop({
    type: String,
    required: true,
  })
  appId: string;

  @Prop({
    type: String,
    required: true,
  })
  appToken: string;

  @Prop({
    type: String,
    required: true,
  })
  tableId: string;

  @Prop({
    enum: Object.values(LarkBaseTableName),
    type: String,
    required: true,
  })
  tableNameType: LarkBaseTableName;
}

export const LarkBaseSettingSchema =
  SchemaFactory.createForClass(LarkBaseSetting);
