import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '../../../../decorators/public.decorator';
import { LarkBaseSettingService } from './lark-base-setting.service';
import { CreateLarkBaseSettingDto } from './dtos/request';

@ApiTags('lark-base-settings')
@Controller('lark-base-settings')
export class LarkBaseSettingController {
  constructor(
    private readonly larkBaseSettingService: LarkBaseSettingService,
  ) {}
  @Post()
  @Public()
  async create(@Body() body: CreateLarkBaseSettingDto) {
    return this.larkBaseSettingService.create(body);
  }
}
