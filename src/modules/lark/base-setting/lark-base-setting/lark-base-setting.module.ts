import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  LarkBaseSetting,
  LarkBaseSettingSchema,
} from './lark-base-setting.schema';
import { LarkBaseSettingService } from './lark-base-setting.service';
import { LarkBaseSettingLoaderService } from './lark-base-setting-loader.service';
import { LarkAppModule } from '../../app/lark-app/lark-app.module';
import { LarkBaseSettingController } from './lark-base-setting.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: LarkBaseSetting.name,
        schema: LarkBaseSettingSchema,
        collection: 'lark-base-settings',
      },
    ]),
    LarkAppModule,
  ],
  controllers: [LarkBaseSettingController],
  providers: [LarkBaseSettingService, LarkBaseSettingLoaderService],
  exports: [MongooseModule, LarkBaseSettingService],
})
export class LarkBaseSettingModule {}
