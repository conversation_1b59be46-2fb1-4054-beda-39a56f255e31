import { LarkAppService } from './../lark-app/lark-app.service';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { LarkBaseSettingService } from './lark-base-setting.service';

@Injectable()
export class LarkBaseSettingLoaderService implements OnModuleInit {
  constructor(
    private readonly larkBaseSettingService: LarkBaseSettingService,
    private readonly larkAppService: LarkAppService,
  ) {}

  async onModuleInit() {
    const appId = 'cli_a77a07c29bf85029';
    const larkApp = await this.larkAppService.findOne({
      appId: appId,
    });
    if (!larkApp) {
      await this.larkBaseSettingService.create({
        appId,
        appToken: 'T4DYbdNEZago14s1S50uSTXXsIc',
        tableId: 'tbl5jYk3wYGoWyWE',
      });
    }
  }
}
