import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../../utils/descriptions';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateLarkBaseSettingDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.larkBaseSetting.validator.appId,
  })
  @IsNotEmpty()
  @IsString()
  appId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.larkBaseSetting.validator.appToken,
  })
  @IsNotEmpty()
  @IsString()
  appToken: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.larkBaseSetting.validator.tableId,
  })
  @IsNotEmpty()
  @IsString()
  tableId: string;

  @ApiProperty({
    type: 'string',
    required: true,
    description: description.larkBaseSetting.validator.tableNameType,
  })
  @IsNotEmpty()
  @IsString()
  tableNameType: string;
}
