import * as moment from 'moment';
import { LarkAppService } from './app/lark-app/lark-app.service';
import {
  AppType,
  BotTemplate,
  NotificationStatus,
  NotificationType,
} from '../../utils/enums';
import { LarkChatSettingService } from './chat-setting/lark-chat-setting/lark-chat-setting.service';
import { BotLark } from '../bot/lark/index';
import { Injectable } from '@nestjs/common';
import { ShopeeItemCommentService } from '../shopee/comment-list/shopee-comment-list/shopee-comment.service';
import { LarkNotificationService } from './notification/lark-notification/lark-notification.service';
import { ShopeeItemService } from '../shopee/item/shopee-item/shopee-item.service';
import { getStarIcon } from '../../utils/common';

@Injectable()
export class LarkService {
  constructor(
    private readonly botLark: BotLark,
    private readonly larkChatSettingService: LarkChatSettingService,
    private readonly larkAppService: LarkAppService,
    private readonly shopeeItemCommentService: ShopeeItemCommentService,
    private readonly shopeeItemService: ShopeeItemService,
    private readonly larkNotificationService: LarkNotificationService,
  ) {}

  async handleWebhook(body: any, appId: string) {
    console.log('body', body);
    const { challenge, event } = body;
    if (challenge) {
      return { challenge };
    }

    if (event && event.type === 'remove_bot') {
      const chatId = event['open_chat_id'];
      await this.handleBotRemove(chatId);
    }

    if (event && event.type === 'add_bot') {
      const chatId = event['open_chat_id'];
      await this.handleBotAdd(chatId, appId);
    }
    return {
      success: true,
    };
  }

  async handleBotRemove(chatId: string) {
    await this.larkChatSettingService.updateByChatId(chatId);
  }

  async handleBotAdd(chatId: string, appId: string) {
    await this.sendMessageToGroup({
      appId,
      chatId: chatId,
      msgType: 'interactive',
      card: this.botLark.getTemplate(BotTemplate.welcomeGroup, {
        chatId: chatId,
      }),
    });

    const larkApp = await this.larkAppService.findOne({
      env: process.env.LARK_ENV,
      appType: AppType.notifyFeedBackUser,
      deletedAt: null,
    });
    if (larkApp) {
      await this.larkChatSettingService.upsert({
        appId: larkApp.appId,
        chatId: chatId,
        deletedAt: null,
      });
    }
  }

  getTemplate(template, params) {
    return this.botLark.getTemplate(template, params);
  }

  async sendMessageToGroup({
    chatId,
    text,
    msgType = 'text',
    content = {},
    card = {},
    appId,
  }: {
    chatId: string;
    text?: string;
    msgType?: string;
    content?: Object;
    card?: Object;
    appId: string;
  }) {
    return this.botLark.sendMessage(
      {
        chatId: chatId,
        text,
        msgType,
        content,
        card,
      },
      appId,
    );
  }

  async sendMessageToExternalGroup({
    chatId,
    baseUrl,
    text,
    msgType = 'text',
    content = {},
    card = {},
  }: {
    chatId: string;
    baseUrl: string;
    text?: string;
    msgType?: string;
    content?: Object;
    card?: Object;
  }) {
    return this.botLark.sendMessageToExternalGroup({
      chatId,
      baseUrl,
      text,
      msgType,
      content,
      card,
    });
  }

  async testSendMessageToGroup(query: any) {
    const content = {
      itemName: query.itemName,
      ratingStar: Number(query.ratingStar),
      ratingStarIcon: getStarIcon(Number(query.ratingStar)),
      buyerUsername: query.buyerUsername,
      comment: query.comment,
      orderSn: query.orderSn,
      createTime: moment
        .unix(Number(query.createTime))
        .format('DD/MM/YYYY HH:mm:ss'),
    };
    await this.sendMessageToGroup({
      appId: query.appId,
      chatId: query.chatId,
      msgType: 'interactive',
      card: this.botLark.getTemplate(BotTemplate.sendFeedback, content),
    });

    return;
  }

  async sendFeedBackShopeeToLark() {
    const larkApp = await this.larkAppService.findOne({
      env: process.env.LARK_ENV,
      appType: AppType.notifyFeedBackUser,
      deletedAt: null,
    });
    if (!larkApp) return;

    const larkChatSettings = await this.larkChatSettingService.find({
      deletedAt: null,
    });

    for (const larkChatSetting of larkChatSettings) {
      const items = await this.shopeeItemService.find(
        { deletedAt: null },
        {
          _id: 1,
          itemId: 1,
          itemName: 1,
        },
      );
      for (const item of items) {
        const lastCreateTime =
          await this.larkNotificationService.getLastCreateTimeItem({
            key: 'itemId',
            chatId: larkChatSetting.chatId,
            value: String(item.itemId),
            deletedAt: null,
          });

        const itemsLastCommentFromApi =
          await this.shopeeItemCommentService.getLastItemCommentFromApi(
            larkChatSetting.ratingStar,
            item.itemId,
            lastCreateTime,
          );
        for (const itemComment of itemsLastCommentFromApi) {
          const content = {
            itemName: itemComment.itemName,
            ratingStar: itemComment.ratingStar,
            ratingStarIcon: getStarIcon(itemComment.ratingStar),
            buyerUsername: itemComment.buyerUsername,
            comment: itemComment.comment,
            orderSn: itemComment.orderSn,
            createTime: moment
              .unix(itemComment.createTime)
              .format('DD/MM/YYYY HH:mm:ss'),
          };

          if (larkChatSetting.appId) {
            await this.sendMessageToGroup({
              appId: larkChatSetting.appId,
              chatId: larkChatSetting.chatId,
              msgType: 'interactive',
              card: this.botLark.getTemplate(BotTemplate.sendFeedback, content),
            });
          }

          if (!larkChatSetting.appId) {
            await this.sendMessageToExternalGroup({
              baseUrl: 'https://open.larksuite.com',
              chatId: larkChatSetting.chatId,
              msgType: 'interactive',
              card: this.botLark.getTemplate(BotTemplate.sendFeedback, content),
            });
          }

          await this.larkNotificationService.create({
            chatId: larkChatSetting.chatId,
            key: 'itemId',
            value: String(itemComment.itemId),
            createTime: itemComment.createTime,
            status: NotificationStatus.success,
            type: NotificationType.feedBack,
            contentJson: content,
          });
        }
      }
    }
  }
}
