import * as moment from 'moment';
import { LarkNotificationService } from './../lark-notification/lark-notification.service';
import { RedisService } from './../storages/redis/redis.service';
import { keyBy } from 'lodash';
import { LarkBaseTableService } from './../lark-base-table/lark-base-table.service';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { LarkAppService } from '../lark-app/lark-app.service';
import { LarkBaseApi } from '../platforms/lark/api/base';
import { ShopeeItemCommentService } from '../shopee-comment-list/shopee-comment.service';
import {
  LarkBaseTableName,
  NotificationStatus,
  NotificationType,
  RedisKey,
} from '../../utils/enums';
import { LarkBaseSettingService } from '../lark-base-setting/lark-base-setting.service';
import {
  LarkBaseConfig,
  LarkBaseConfigTableNameType,
  LarkBaseSendFeedBackFromShopee,
} from './lark-base.type';
import { LarkBaseConfigCommonQueryDto } from '../../shared/dtos/common.dto';
import {
  createFieldsItemComment,
  createFieldsShopeeAds,
} from './common/field-table';
import { ShopeeAdsService } from '../shopee-ads/shopee-ads.service';

@Injectable()
export class LarkBaseService {
  private readonly logger = new Logger(LarkBaseService.name);
  constructor(
    private readonly shopeeItemCommentService: ShopeeItemCommentService,
    private readonly shopeeAdsService: ShopeeAdsService,
    private readonly larkAppService: LarkAppService,
    private readonly larkBaseApi: LarkBaseApi,
    private readonly larkBaseSettingService: LarkBaseSettingService,
    private readonly larkBaseTableService: LarkBaseTableService,
    private readonly larkNotificationService: LarkNotificationService,
    private readonly redisService: RedisService,
  ) {}

  async sendFeedBackShopeeToLarkBase(config: LarkBaseSendFeedBackFromShopee) {
    const { appType, tableNameType, env } = config;

    const larkApp = await this.larkAppService.findOne({
      appType,
      env,
      deletedAt: null,
    });
    if (!larkApp) return;

    const configApp = await this.getConfigByTableNameType({
      appId: larkApp.appId,
      tableNameType,
    });

    const options = {
      appToken: configApp.appToken,
      tableId: configApp.tableId,
      accessToken: configApp.tenantAccessToken,
    };

    const larkBaseTables = await this.larkBaseTableService.find({
      deletedAt: null,
      tableId: options.tableId,
    });

    const shopeeItemComments =
      await this.shopeeItemCommentService.getItemCommentByDay(2);

    const keyLarkBaseTables = keyBy(larkBaseTables, 'fieldCode');
    const baseRecords = [];

    for (const shopeeItemComment of shopeeItemComments) {
      const recordExist = await this.larkNotificationService.findOne({
        key: 'commentId',
        value: String(shopeeItemComment.commentId),
      });

      if (!recordExist) {
        const baseRecord = {
          fields: {},
        };
        Object.keys(shopeeItemComment.toJSON()).map((key) => {
          if (keyLarkBaseTables[key]) {
            baseRecord.fields[keyLarkBaseTables[key].fieldName] =
              keyLarkBaseTables[key].fieldFormat === 'number'
                ? Number(shopeeItemComment[key])
                : keyLarkBaseTables[key].fieldFormat === 'date'
                ? moment(
                    shopeeItemComment[key],
                    'YYYY-MM-DD HH:mm:ss',
                  ).valueOf()
                : String(shopeeItemComment[key]);
          }
        });

        baseRecords.push(baseRecord);
      }
    }

    if (baseRecords.length) {
      const createRecordsFromApi = await this.larkBaseApi.createRecords(
        options,
        {
          records: baseRecords,
        },
      );

      for (const record of createRecordsFromApi.data.records) {
        const commentId = record.fields['ID'];
        const exist = shopeeItemComments.find(
          (comment) => String(comment.commentId) === commentId,
        );
        if (exist) {
          await this.larkNotificationService.create({
            tableId: options.tableId,
            key: 'commentId',
            value: String(commentId),
            createTime: exist.createTime,
            status: NotificationStatus.success,
            type: NotificationType.base,
            contentJson: record,
          });
        }
      }
    }
  }

  async sendAdsShopeeToLarkBase(config: LarkBaseSendFeedBackFromShopee) {
    const { appType, env, tableNameType } = config;

    const larkApp = await this.larkAppService.findOne({
      appType,
      env,
      deletedAt: null,
    });
    if (!larkApp) return;

    const configApp = await this.getConfigByTableNameType({
      appId: larkApp.appId,
      tableNameType,
    });

    const options = {
      appToken: configApp.appToken,
      tableId: configApp.tableId,
      accessToken: configApp.tenantAccessToken,
    };

    const larkBaseTables = await this.larkBaseTableService.find({
      deletedAt: null,
      tableId: options.tableId,
    });

    const shopeeAds = await this.shopeeAdsService.find(
      {
        deletedAt: null,
      },
      {},
      { limit: 1000 },
    );

    const keyLarkBaseTables = keyBy(larkBaseTables, 'fieldCode');
    const baseRecords = [];

    for (const shopeeAd of shopeeAds) {
      const recordExist = await this.larkNotificationService.findOne({
        key: 'campaignId',
        value: String(shopeeAd.campaignId),
      });

      if (!recordExist) {
        const baseRecord = {
          fields: {
            ['ID']: String(shopeeAd.campaignId),
            [keyLarkBaseTables['adName'].fieldName]: shopeeAd.commonInfo.adName,
            [keyLarkBaseTables['adType'].fieldName]: shopeeAd.commonInfo.adType,
            [keyLarkBaseTables['campaignStatus'].fieldName]:
              shopeeAd.commonInfo.campaignStatus,
            [keyLarkBaseTables['biddingMethod'].fieldName]:
              shopeeAd.commonInfo.biddingMethod,
            [keyLarkBaseTables['campaignPlacement'].fieldName]:
              shopeeAd.commonInfo.campaignPlacement,
            [keyLarkBaseTables['campaignBudget'].fieldName]: Number(
              shopeeAd.commonInfo.campaignBudget,
            ),
            [keyLarkBaseTables['startTime'].fieldName]: moment(
              shopeeAd.commonInfo.campaignDuration.startTime,
            ).valueOf(),
            [keyLarkBaseTables['endTime'].fieldName]: moment(
              shopeeAd.commonInfo.campaignDuration.endTime,
            ).valueOf(),
          },
        };

        baseRecords.push(baseRecord);
      }
    }

    if (baseRecords.length) {
      const createRecordsFromApi = await this.larkBaseApi.createRecords(
        options,
        {
          records: baseRecords,
        },
      );

      for (const record of createRecordsFromApi.data.records) {
        const campaignId = record.fields['ID'];
        const exist = shopeeAds.find(
          (ad) => String(ad.campaignId) === campaignId,
        );
        if (exist) {
          await this.larkNotificationService.create({
            tableId: options.tableId,
            key: 'campaignId',
            value: String(campaignId),
            createTime: moment(record.createdAt).unix(),
            status: NotificationStatus.success,
            type: NotificationType.base,
            contentJson: record,
          });
        }
      }
    }
  }

  async getConfigByTableNameType(config: LarkBaseConfigTableNameType) {
    const { appId, tableNameType } = config;
    if (!appId || !tableNameType) {
      throw new BadRequestException('appId or tableNameType is required');
    }
    const larkApp = await this.larkAppService.findOne({
      appId: config.appId,
    });
    if (!larkApp) return null;

    const larkBaseSetting = await this.larkBaseSettingService.findOne({
      appId: larkApp.appId,
      tableNameType: config.tableNameType,
    });

    if (!larkBaseSetting) return null;

    const configRedis = await this.redisService.getValue(
      `${RedisKey.lark}_${larkApp.appId}`,
    );

    return {
      ...larkBaseSetting.toJSON(),
      ...configRedis,
    };
  }

  async getConfig(config: LarkBaseConfig) {
    const { appId, tableId } = config;
    if (!appId || !tableId) {
      throw new BadRequestException('appId or tableId is required');
    }
    const larkApp = await this.larkAppService.findOne({
      appId,
    });
    if (!larkApp) return null;

    const larkBaseSetting = await this.larkBaseSettingService.findOne({
      appId: larkApp.appId,
      tableId: tableId,
    });

    if (!larkBaseSetting) return null;

    const configRedis = await this.redisService.getValue(
      `${RedisKey.lark}_${larkApp.appId}`,
    );

    return {
      ...larkBaseSetting.toJSON(),
      ...configRedis,
    };
  }

  async createBaseTable(query: LarkBaseConfigCommonQueryDto) {
    const { appId, tableId, tableNameType } = query;
    if (!appId || !tableId || !tableNameType) {
      throw new BadRequestException(
        'appId or tableId or tableNameType is required',
      );
    }

    const config = await this.getConfig({ appId, tableId });

    const options = {
      appToken: config.appToken,
      tableId: config.tableId,
      accessToken: config.tenantAccessToken,
    };

    const resultFieldApi = await this.larkBaseApi.getFields(options, {
      pageSize: 50,
      textFieldAsArray: 'True',
    });

    const fields = resultFieldApi.data.items;

    let createFields = [];

    switch (tableNameType) {
      case LarkBaseTableName.shopeeItemComment:
        createFields = createFieldsItemComment;
        break;
      case LarkBaseTableName.shopeeAds:
        createFields = createFieldsShopeeAds;
        break;
      default:
        throw new BadRequestException('tableNameType is not supported');
    }

    const createFieldResponse = [];
    for (const createField of createFields) {
      // const fieldExist = fields.find(
      //   (field) => field['field_name'] === createField.fieldName,
      // );
      // if (fieldExist) continue;

      const larkBaseTable = await this.larkBaseTableService.findOne({
        fieldCode: createField.fieldCode,
        deletedAt: null,
      });
      if (!larkBaseTable) {
        const response = await this.larkBaseApi.createField(options, {
          fieldName: createField.fieldName,
          type: createField.type,
          isPrimary: true,
        });
        if (response?.code !== 0) {
          throw new InternalServerErrorException(response?.msg);
        }
        console.log('response', response);
        createFieldResponse.push(response);
        this.larkBaseTableService.upsert({
          fieldCode: createField.fieldCode,
          fieldId: response.data['field']['field_id'],
          fieldName: response.data['field']['field_name'],
          tableId: options.tableId,
          fieldFormat: createField.format,
          deletedAt: null,
        });
      }
    }
    return createFieldResponse;
  }

  async createBaseTableRecords(config: LarkBaseConfig) {
    const configApp = await this.getConfig(config);

    const options = {
      appToken: configApp.appToken,
      tableId: configApp.tableId,
      accessToken: configApp.tenantAccessToken,
    };

    const larkBaseTables = await this.larkBaseTableService.find({
      deletedAt: null,
      tableId: options.tableId,
    });

    let page = 1;

    while (true) {
      const limit = 500;
      const skip = (page - 1) * limit;
      const shopeeItemComments = await this.shopeeItemCommentService.find(
        {
          deletedAt: null,
        },
        {},
        { limit: limit, skip: skip },
      );
      if (!shopeeItemComments.length) return;
      const keyLarkBaseTables = keyBy(larkBaseTables, 'fieldCode');
      const baseRecords = [];
      for (const shopeeItemComment of shopeeItemComments) {
        const recordExist = await this.larkNotificationService.findOne({
          key: 'commentId',
          value: String(shopeeItemComment.commentId),
        });
        if (!recordExist) {
          const baseRecord = {
            fields: {},
          };
          Object.keys(shopeeItemComment.toJSON()).map((key) => {
            if (keyLarkBaseTables[key]) {
              baseRecord.fields[keyLarkBaseTables[key].fieldName] =
                keyLarkBaseTables[key].fieldFormat === 'number'
                  ? Number(shopeeItemComment[key])
                  : String(shopeeItemComment[key]);
            }
          });

          baseRecords.push(baseRecord);
        }
      }
      if (baseRecords.length) {
        const createRecordsFromApi = await this.larkBaseApi.createRecords(
          options,
          {
            records: baseRecords,
          },
        );

        for (const record of createRecordsFromApi.data.records) {
          const commentId = record.fields['ID'];
          await this.larkNotificationService.create({
            tableId: options.tableId,
            key: 'commentId',
            value: String(commentId),
            createTime: moment().unix(),
            status: NotificationStatus.success,
            type: NotificationType.base,
            contentJson: record,
          });
        }
      }

      page++;
    }
  }

  async deleteBaseTableRecords(config: LarkBaseConfig): Promise<void> {
    const configApp = await this.getConfig(config);

    const options = {
      appToken: configApp.appToken,
      tableId: configApp.tableId,
      accessToken: configApp.tenantAccessToken,
    };

    await this.larkBaseApi.deleteRecords(options, {
      records: ['recwNXzPQv'],
    });
  }
}
