import {
  ApiBadRequestResponse,
  ApiExcludeController,
  ApiExcludeEndpoint,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../utils/enums';
import { Controller, Get, Query } from '@nestjs/common';
import { description, getDescription } from '../../utils/descriptions';
import { LarkBaseService } from './lark-base.service';
import { LarkBaseConfigCommonQueryDto } from '../../shared/dtos/common.dto';
import { Public } from '../../decorators/public.decorator';

@ApiTags(RouteName.larkBases)
@Controller(RouteName.larkBases)
@ApiExcludeController()
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class LarkBaseController {
  constructor(private readonly larkBaseService: LarkBaseService) {}

  @Get('create-table')
  @ApiExcludeEndpoint()
  @Public()
  @ApiOperation({
    summary: getDescription(RouteName.larkBases, description.controller.getOne),
  })
  async createBaseTable(
    @Query() query: LarkBaseConfigCommonQueryDto,
  ): Promise<any> {
    return this.larkBaseService.createBaseTable(query);
  }

  @Get('create-records')
  @ApiExcludeEndpoint()
  @ApiOperation({
    summary: getDescription(RouteName.larkBases, description.controller.getOne),
  })
  async createBaseTableRecords(
    @Query() query: LarkBaseConfigCommonQueryDto,
  ): Promise<any> {
    return this.larkBaseService.createBaseTableRecords(query);
  }

  @Get('delete-records')
  @ApiExcludeEndpoint()
  @ApiOperation({
    summary: getDescription(RouteName.larkBases, description.controller.getOne),
  })
  async deleteBaseTableRecords(
    @Query() query: LarkBaseConfigCommonQueryDto,
  ): Promise<any> {
    return this.larkBaseService.deleteBaseTableRecords(query);
  }

  @Get('test-sync')
  @ApiExcludeEndpoint()
  @Public()
  @ApiOperation({
    summary: getDescription(RouteName.larkBases, description.controller.getOne),
  })
  async testSync(
    @Query() query: { appType: string; env: string; tableNameType: string },
  ) {
    return this.larkBaseService.sendAdsShopeeToLarkBase({
      appType: query.appType || 'notifyFeedBackUser',
      env: query.env || process.env.LARK_ENV,
      tableNameType: query.tableNameType || 'shopeeAds',
    });
  }
}
