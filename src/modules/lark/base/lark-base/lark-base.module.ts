import { Module } from '@nestjs/common';
import { LarkBaseService } from './lark-base.service';
import { LarkBaseController } from './lark-base.controller';
import { LarkBaseApi } from '../../../platforms/lark/api/base';
import { LarkAppModule } from '../../app/lark-app/lark-app.module';
import { LarkBaseTableModule } from '../../base-table/lark-base-table/lark-base-table.module';
import { ShopeeItemCommentModule } from '../../../shopee/comment-list/shopee-comment-list/shopee-comment.module';
import { LarkBaseSettingModule } from '../../base-setting/lark-base-setting/lark-base-setting.module';
import { RedisModule } from '../../../platforms/storages/storages/redis/redis.module';
import { LarkNotificationModule } from '../../notification/lark-notification/lark-notification.module';
import { ShopeeAdsModule } from '../../../shopee/ads/shopee-ads/shopee-ads.module';

@Module({
  imports: [
    ShopeeItemCommentModule,
    ShopeeAdsModule,
    LarkAppModule,
    LarkBaseTableModule,
    LarkBaseSettingModule,
    LarkNotificationModule,
    RedisModule,
  ],
  providers: [LarkBaseService, LarkBaseApi],
  controllers: [LarkBaseController],
  exports: [LarkBaseService],
})
export class LarkBaseModule {}
