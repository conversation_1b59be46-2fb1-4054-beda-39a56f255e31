import { Module } from '@nestjs/common';
import { LarkBaseService } from './lark-base.service';
import { LarkBaseController } from './lark-base.controller';
import { LarkBaseApi } from '../platforms/lark/api/base';
import { LarkAppModule } from '../lark-app/lark-app.module';
import { LarkBaseTableModule } from '../lark-base-table/lark-base-table.module';
import { ShopeeItemCommentModule } from '../shopee-comment-list/shopee-comment.module';
import { LarkBaseSettingModule } from '../lark-base-setting/lark-base-setting.module';
import { RedisModule } from '../storages/redis/redis.module';
import { LarkNotificationModule } from '../lark-notification/lark-notification.module';
import { ShopeeAdsModule } from '../shopee-ads/shopee-ads.module';

@Module({
  imports: [
    ShopeeItemCommentModule,
    ShopeeAdsModule,
    LarkAppModule,
    LarkBaseTableModule,
    LarkBaseSettingModule,
    LarkNotificationModule,
    RedisModule,
  ],
  providers: [LarkBaseService, LarkBaseApi],
  controllers: [LarkBaseController],
  exports: [LarkBaseService],
})
export class LarkBaseModule {}
