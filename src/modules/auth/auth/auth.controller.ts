import {
  ApiBadRequestResponse,
  ApiExcludeEndpoint,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { RouteName } from '../../../utils/enums';
import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { description, getDescription } from '../../../utils/descriptions';
import { ApplicationCallBackDto } from './dtos/auth';
import { AuthService } from './auth.service';
import { LoginDto } from './dtos/login';
import { Public } from '../../../decorators/public.decorator';
import { ApiExcludeAll } from '../../../decorators/global-swagger-exclude.decorator';

@ApiTags(RouteName.auths)
@Controller(RouteName.auths)
@ApiOkResponse({
  description: description.httpStatus.ok,
  isArray: true,
})
@ApiForbiddenResponse({
  description: description.httpStatus.forbidden,
})
@ApiUnauthorizedResponse({ description: description.httpStatus.unAuthorized })
@ApiBadRequestResponse({ description: description.httpStatus.badRequest })
@ApiInternalServerErrorResponse({
  description: description.httpStatus.interalServer,
})
@ApiNotFoundResponse({ description: description.httpStatus.notFound })
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('/login')
  @ApiExcludeEndpoint()
  @ApiOperation({
    summary: getDescription(RouteName.auths, description.controller.login),
  })
  async login(@Body() loginDto: LoginDto): Promise<any> {
    return this.authService.login(loginDto);
  }

  @Public()
  @Get('/:name/:configId/call-back')
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: description.auths.validator.callBack,
  })
  @ApiOperation({
    summary: getDescription(RouteName.auths, description.controller.callBack),
  })
  async handleCallBack(
    @Param('name') name: string,
    @Param('configId') configId: string,
    @Query() query: ApplicationCallBackDto,
  ): Promise<any> {
    return this.authService.handleCallBackFromApplication(
      name,
      configId,
      query,
    );
  }
}
