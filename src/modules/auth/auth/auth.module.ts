import { Module } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { ShopeeApiModule } from '../shopee-api/shopee-api.module';
import { ShopeeConfigurationModule } from '../shopee-configuration/shopee-configuration.module';
import { AuthService } from './auth.service';
import { RedisModule } from '../storages/redis/redis.module';
import { UserModule } from '../users/user.module';
import { JwtModule } from '@nestjs/jwt';
import { JwtStrategy } from './strategies/jwt.strategy';

@Module({
  imports: [
    ShopeeConfigurationModule,
    ShopeeApiModule,
    RedisModule,
    UserModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'secretKey',
      signOptions: { expiresIn: process.env.JWT_EXPIRE || '1h' },
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
