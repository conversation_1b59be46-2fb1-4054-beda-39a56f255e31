import { Injectable, OnModuleInit } from '@nestjs/common';
import { UserService } from './user.service';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UserLoaderService implements OnModuleInit {
  constructor(private readonly userService: UserService) {}

  async onModuleInit() {
    const hardcodedPassword = 'Admin@12345';
    const hashedPassword = await bcrypt.hash(hardcodedPassword, 10); // Hash the password with 10 rounds

    // Check if the admin user already exists
    const adminUser = await this.userService.findUserByUsername('admin');

    if (!adminUser) {
      // Create the admin user with the hashed password
      await this.userService.createUser('admin', hashedPassword);

      console.log('Admin user created with hashed password.');
    } else {
      console.log('Admin user already exists.');
    }
  }
}
