import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { description } from '../../../utils/descriptions';

export class ApplicationCallBackDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.auths.validator.code,
  })
  @IsNotEmpty()
  @IsString()
  code: string;
}

export class ShopeeCallBackDto extends ApplicationCallBackDto {
  @ApiProperty({
    type: 'string',
    required: true,
    description: description.auths.validator.shopeeShopId,
  })
  @IsNotEmpty()
  @IsString()
  shopId: string;
}
