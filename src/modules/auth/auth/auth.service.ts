import * as bcrypt from 'bcrypt';
import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ShopeeApiService } from '../shopee-api/shopee-api-service';
import { ShopeeConfigurationService } from '../shopee-configuration/shopee-configuration.service';
import { ApplicationName, RedisKey } from '../../utils/enums';
import { ApplicationCallBackDto, ShopeeCallBackDto } from './dtos/auth';
import { Types } from 'mongoose';
import { RedisService } from '../storages/redis/redis.service';
import { UserService } from '../users/user.service';
import { JwtService } from '@nestjs/jwt';
import { LoginDto } from './dtos/login';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly shopeeApiService: ShopeeApiService,
    private readonly shopeeConfigurationService: ShopeeConfigurationService,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  async validateUser(username: string, password: string): Promise<any> {
    const user = await this.userService.findUserByUsername(username);
    if (user && (await bcrypt.compare(password, user.password))) {
      const { password, ...result } = user.toObject();
      return result;
    }
    return null;
  }

  async login(loginDto: LoginDto): Promise<any> {
    const { username, password } = loginDto;

    // Find the user by username
    const user = await this.userService.findUserByUsername(username);
    if (!user) {
      throw new Error('User not found');
    }

    // Validate the password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new Error('Invalid password');
    }

    // Generate JWT token
    const payload = { username: user.username, sub: user._id }; // Customize the payload as needed

    try {
      const accessToken = await this.jwtService.signAsync(payload, {
        secret: process.env.JWT_SECRET, // Use the secret from environment variables
        expiresIn: process.env.JWT_EXPIRE, // Token expiration time
      });

      return { accessToken };
    } catch (error) {
      throw new InternalServerErrorException('Error generating JWT');
    }
  }

  async handleCallBackFromApplication(
    name: string,
    configId: string,
    query: ApplicationCallBackDto,
  ) {
    switch (name) {
      case ApplicationName.shopee: {
        const queryFromShopee: Partial<ShopeeCallBackDto> = query;

        const shopeeConfig = await this.shopeeConfigurationService.findOne({
          _id: new Types.ObjectId(configId),
        });
        if (!shopeeConfig) {
          throw new NotFoundException(`Not found shopee configuration`);
        }

        const resultToken = await this.shopeeApiService.getAccessToken(
          queryFromShopee.code,
          shopeeConfig,
        );
        if (resultToken) {
          const shopId =
            queryFromShopee['shop_id'] && Number(queryFromShopee['shop_id']);
          const accessToken = resultToken['access_token'];
          const resultShopProfile = await this.shopeeApiService.getShopProfile({
            shopId,
            partnerId: shopeeConfig.partnerId,
            accessToken,
          });
          const config = {
            accessToken: accessToken,
            refreshToken: resultToken['refresh_token'],
            expireIn: resultToken['expire_in'],
            shopId: shopId,
            mainAccountId:
              queryFromShopee['main_account_id'] &&
              Number(queryFromShopee['main_account_id']),
            shopName: resultShopProfile.response['shop_name'],
          };
          await this.shopeeConfigurationService.update(
            shopeeConfig._id,
            config,
          );

          await this.redisService.setValue(
            `${RedisKey.shopee}_${shopId}`,
            config,
          );

          this.logger.log('Config Shopee Successfully');
          return {
            status: HttpStatus.OK,
            message: 'Successfully',
          };
        }

        throw new InternalServerErrorException(
          'Receive accesstoken shopee failed',
        );
      }
      default: {
        break;
      }
    }
  }
}
