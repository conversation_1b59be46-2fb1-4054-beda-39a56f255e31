import { ShopeeItemStatus } from './config';

export type ShopeeApiConfigType = {
  baseUrl: string;
  partnerId: number;
  partnerKey: string;
  shopId: number;
  mainAccountId: number;
  accessToken: string;
  refreshToken: string;
  expireIn: number;
};

export type ShopeeApiShopProfile = {
  accessToken: string;
  shopId: number;
  partnerId: number;
};

export type ShopeeApiMakeRequestOptions = {
  url: string;
  headers?: Record<string, string>;
  params?: any;
  data?: any;
};

export type ShopeeGetItemList = {
  offset: number;
  itemStatus?: ShopeeItemStatus;
  pageSize?: number;
  updateTimeFrom?: number;
  updateTimeTo?: number;
};

export type ShopeeGetItemComment = {
  commentId: number;
  page: number;
  pageSize: number;
  itemId: number;
};

export type ShopeeGetOrderList = {
  partnerId: number;
  timeFrom: number;
  timeTo: number;
  cursor?: string;
  pageSize?: number;
  orderStatus?: string;
  timeRangeField?: string;
  responseOptionalFields?: string;
};

export type ShopeeGetOrderDetail = {
  partnerId: number;
  orderSnList: string[]; // list of order SNs
  requestOrderStatusPending?: boolean;
  responseOptionalFields?: string;
};

export type ShopeeGetCampainList = {
  partnerId: number;
  offset: number;
  pageSize: number;
  adType: string[];
};

export type ShopeeGetCampainInfo = {
  partnerId: number;
  infoTypeList: string; //1.Common Info 2.Manual Bidding Info 3.Auto Bidding Info 4.Auto Product Ads Info
  campaignIdList: string;
};
