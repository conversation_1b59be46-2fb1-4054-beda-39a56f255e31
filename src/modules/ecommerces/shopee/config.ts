export enum ShopeeApiUrl {
  getToken = '/api/v2/auth/token/get',
  getTokenByRefreshToken = '/api/v2/auth/access_token/get',
  getItemList = '/api/v2/product/get_item_list',
  getItemBaseInfo = '/api/v2/product/get_item_base_info',
  getItemComment = '/api/v2/product/get_comment',
  getShopProfile = '/api/v2/shop/get_profile',
  getOrderList = '/api/v2/order/get_order_list',
  getOrderDetail = '/api/v2/order/get_order_detail',
  getTotalBalance = '/api/v2/ads/get_total_balance',
  getAdsHourlyPerformance = '/api/v2/ads/get_all_cpc_ads_hourly_performance',
  getProductCampaignDailyPerformance = '/api/v2/ads/get_product_campaign_daily_performance',
  getProductLevelCampaignIdList = '/api/v2/ads/get_product_level_campaign_id_list',
  getProductLevelCampaignSettingInfo = '/api/v2/ads/get_product_level_campaign_setting_info',
}

export enum ShopeeItemStatus {
  NORMAL = 'NORMAL',
  BANNED = 'BANNED',
  DELETED = 'DELETED',
  UNLIST = 'UNLIST',
  UNLIST_INACTIVE = 'UNLIST_INACTIVE',
}
