import { RedisService } from './../../storages/redis/redis.service';
import axios from 'axios';
import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import {
  ShopeeApiConfigType,
  ShopeeApiMakeRequestOptions,
  ShopeeApiShopProfile,
  ShopeeGetCampainInfo,
  ShopeeGetCampainList,
  ShopeeGetItemComment,
  ShopeeGetItemList,
  ShopeeGetOrderDetail,
  ShopeeGetOrderList,
} from './types';
import { getTimeStamp } from '../../../utils/time';
import { ShopeeApiUrl, ShopeeItemStatus } from './config';
import { generateSignature } from '../../../utils/common';
import { omitBy, isNil } from 'lodash';
import { convertToSnakeCase } from '../../../utils/case-converter';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RedisKey } from '../../../utils/enums';
@Injectable()
export class ShopeeApi {
  constructor(
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.eventEmitter = eventEmitter;
  }

  private getConfigFromRedis(key): Promise<ShopeeApiConfigType> {
    if (!key) {
      throw new BadRequestException('partnerId is required');
    }
    return this.redisService.getValue(`${RedisKey.shopee}_${key}`);
  }

  private validateAccessToken(config: ShopeeApiConfigType): void {
    if (!config.accessToken) {
      throw new BadRequestException('accessToken is required');
    }
  }

  private getCommonField(config: ShopeeApiConfigType) {
    return {
      accessToken: config.accessToken,
      shopId: config.shopId,
      partnerId: config.partnerId,
    };
  }

  private getCommonHeader() {
    return {
      'Content-Type': 'application/json',
    };
  }

  private getSignAccessToken(path: string, config: ShopeeApiConfigType) {
    const timestamp = getTimeStamp();
    const baseString = [config.partnerId, path, timestamp].join('');
    const sign = generateSignature(baseString, config.partnerKey);
    return {
      timestamp,
      sign,
    };
  }

  private getSign(path: string, config: ShopeeApiConfigType) {
    this.validateAccessToken(config);
    const timestamp = getTimeStamp();
    const baseString = [
      config.partnerId,
      path,
      timestamp,
      config.accessToken,
      config.shopId,
    ].join('');
    const sign = generateSignature(baseString, config.partnerKey);
    return {
      timestamp,
      sign,
    };
  }

  private async makeRequest(
    method: string,
    config: ShopeeApiConfigType,
    options: ShopeeApiMakeRequestOptions,
    retry = 0,
  ) {
    const { url, headers, params, data } = options;
    const { timestamp, sign } = this.getSign(url, config);

    try {
      const response = await axios({
        url: `${config.baseUrl}${url}`,
        method,
        headers: headers || this.getCommonHeader(),
        params:
          params &&
          convertToSnakeCase({
            ...params,
            ...this.getCommonField(config),
            timestamp,
            sign,
          }),
        data: data && convertToSnakeCase({ ...data }),
      });
      return response.data;
    } catch (error) {
      switch (error.status) {
        case HttpStatus.FORBIDDEN: {
          if (retry === 3) {
            console.log('Max retry api 3 times');
            break;
          }
          if (
            ['invalid_acceess_token', 'invalid_access_token'].includes(
              error.response.data?.error,
            )
          ) {
            const result = await this.refreshAccessToken(config);
            if (result['access_token']) {
              const newConfig: ShopeeApiConfigType = {
                ...config,
                accessToken: result['access_token'],
                refreshToken: result['refresh_token'],
                expireIn: result['expire_in'],
              };

              this.eventEmitter.emit('shoppeeConfiguration.update', {
                accessToken: newConfig.accessToken,
                refreshToken: newConfig.refreshToken,
                expireIn: newConfig.expireIn,
              });

              return this.makeRequest(
                method,
                newConfig,
                {
                  url,
                  headers,
                  params: params,
                  data,
                },
                retry++,
              );
            }
          }
        }
      }
      console.log('error', error);
      throw error;
    }
  }

  async getAcessToken(code: string, config: ShopeeApiConfigType) {
    const { timestamp, sign } = this.getSignAccessToken(
      ShopeeApiUrl.getToken,
      config,
    );

    const params = {
      partnerId: config.partnerId,
      timestamp: timestamp,
      sign: sign,
    };

    const body = omitBy(
      {
        code,
        mainAccountId: config.mainAccountId,
        ...this.getCommonField(config),
      },
      isNil,
    );

    const response = await axios({
      url: `${config.baseUrl}${ShopeeApiUrl.getToken}`,
      method: 'post',
      params: convertToSnakeCase(params),
      data: convertToSnakeCase(body),
      headers: this.getCommonHeader(),
    }).catch((err) => {
      console.log(err);
      throw err;
    });

    return response.data;
  }

  async refreshAccessToken(config: ShopeeApiConfigType) {
    const { timestamp, sign } = this.getSignAccessToken(
      ShopeeApiUrl.getTokenByRefreshToken,
      config,
    );

    const params = {
      partnerId: config.partnerId,
      timestamp: timestamp,
      sign: sign,
    };

    const body = omitBy(
      {
        refreshToken: config.refreshToken,
        ...this.getCommonField(config),
      },
      isNil,
    );
    const response = await axios({
      url: `${config.baseUrl}${ShopeeApiUrl.getTokenByRefreshToken}`,
      method: 'post',
      params: convertToSnakeCase(params),
      data: convertToSnakeCase(body),
      headers: this.getCommonHeader(),
    }).catch((err) => {
      console.log(err);
      throw err;
    });

    return response.data;
  }

  async getItemList(
    {
      offset = 0,
      itemStatus = ShopeeItemStatus.NORMAL,
      pageSize = 10,
      updateTimeFrom,
      updateTimeTo,
    }: ShopeeGetItemList,
    partnerId: number,
  ) {
    const params = omitBy(
      {
        updateTimeFrom: updateTimeFrom, // Example 1611311600
        updateTimeTo: updateTimeTo, // Example 1611311631
        pageSize: pageSize, // Max 50
        itemStatus: itemStatus, // NORMAL, BANNED, DELETED, UNLIST, UNLIST_INACTIVE
        offset: offset,
      },
      isNil,
    );
    const config = await this.getConfigFromRedis(partnerId);
    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getItemList,
      headers: this.getCommonHeader(),
      params: convertToSnakeCase(params),
    });
  }

  async getItemDetail(itemId: string, partnerId: number) {
    const params = {
      itemIdList: itemId,
    };
    const config = await this.getConfigFromRedis(partnerId);
    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getItemBaseInfo,
      params: params,
    });
  }

  async getItemComment(
    {
      commentId = 0,
      page = 0,
      pageSize = 10, // Max 50
      itemId,
    }: ShopeeGetItemComment,
    partnerId: number,
  ) {
    const params = {
      cursor: page,
      pageSize: pageSize,
      itemId: itemId,
      commentId: commentId,
    };
    const config = await this.getConfigFromRedis(partnerId);
    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getItemComment,
      params: params,
    });
  }

  async getShopProfile(params: ShopeeApiShopProfile) {
    const config = await this.getConfigFromRedis(params.partnerId);
    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getShopProfile,
      params,
    });
  }

  async getOrderList({
    partnerId,
    timeFrom,
    timeTo,
    cursor = '',
    pageSize = 20,
    orderStatus,
    timeRangeField = 'create_time',
    responseOptionalFields = 'order_status',
  }: ShopeeGetOrderList) {
    const params = {
      timeFrom: timeFrom,
      timeTo: timeTo,
      cursor,
      pageSize: pageSize,
      orderStatus: orderStatus,
      timeRangeField: timeRangeField,
      responseOptionalFields: responseOptionalFields,
    };

    const config = await this.getConfigFromRedis(partnerId);
    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getOrderList,
      params,
    });
  }

  async getOrderDetail({
    partnerId,
    orderSnList,
    requestOrderStatusPending = true,
    responseOptionalFields = 'total_amount',
  }: ShopeeGetOrderDetail) {
    const params = {
      orderSnList: orderSnList.join(','), // convert array to comma-separated string
      partnerId: partnerId,
      requestOrderStatusPending: requestOrderStatusPending,
      responseOptionalFields: responseOptionalFields,
    };
    const config = await this.getConfigFromRedis(partnerId);
    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getOrderDetail,
      params,
    });
  }

  async getProductLevelCampaignIdList(params: ShopeeGetCampainList) {
    const config = await this.getConfigFromRedis(params.partnerId);

    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getProductLevelCampaignIdList,
      params: params,
    });
  }

  async getProductLevelCampaignSettingInfo(params: ShopeeGetCampainInfo) {
    const config = await this.getConfigFromRedis(params.partnerId);

    return this.makeRequest('get', config, {
      url: ShopeeApiUrl.getProductLevelCampaignSettingInfo,
      params: params,
    });
  }
}
